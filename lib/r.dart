// GENERATED CODE - DO NOT MODIFY BY HAND
// *********************************************************

class R {
  static const String flutter_logo = 'assets/images/flutter_logo.png';
  static const String translate = 'assets/images/translate.svg';
  static const String tab_home = 'assets/images/tab_home.svg';
  static const String microphone_off = 'assets/images/microphone_off.svg';
  static const String microphone = 'assets/images/microphone.svg';
  static const String recording = 'assets/images/recording.svg';
  static const String speed = 'assets/images/speed.svg';
  static const String voice_wave = 'assets/images/voice_wave.svg';
  static const String tab_mine = 'assets/images/tab_mine.svg';
  static const String go_center = 'assets/images/go_center.svg';
  static const String collect = 'assets/images/collect.svg';
  static const String eye_close = 'assets/images/eye_close.svg';
  static const String download = 'assets/images/download.svg';
  static const String subtitle_merge_next = 'assets/images/subtitle_merge_next.svg';
  static const String book = 'assets/images/book.svg';
  static const String subtitle_merge_pre = 'assets/images/subtitle_merge_pre.svg';
  static const String translate_close = 'assets/images/translate_close.svg';
  static const String record = 'assets/images/record.svg';
  static const String plus = 'assets/images/plus.svg';
  static const String alignleft = 'assets/images/alignleft.svg';
  static const String headphones = 'assets/images/headphones.svg';
  static const String speed_half = 'assets/images/speed_half.svg';
  static const String translate_target = 'assets/images/translate_target.svg';
  static const String copy = 'assets/images/copy.svg';
  static const String setting = 'assets/images/setting.svg';
  static const String play = 'assets/images/play.svg';
  static const String eye_open = 'assets/images/eye_open.svg';
  static const String ai = 'assets/images/ai.svg';
  static const String edit_select = 'assets/images/edit_select.svg';
  static const String go_center_off = 'assets/images/go_center_off.svg';
  static const String more = 'assets/images/more.svg';
  static const String heart_select = 'assets/images/heart_select.svg';
  static const String translate_native = 'assets/images/translate_native.svg';
  static const String edit = 'assets/images/edit.svg';
  static const String only_lines_active = 'assets/images/only_lines_active.svg';
  static const String delete = 'assets/images/delete.svg';
  static const String fullscreen_open = 'assets/images/fullscreen_open.svg';
  static const String tab_collect = 'assets/images/tab_collect.svg';
  static const String fullscreen_close = 'assets/images/fullscreen_close.svg';
  static const String tab_data = 'assets/images/tab_data.svg';
  static const String no_data = 'assets/images/no_data.png';
  static const String logo = 'assets/images/logo.svg';
  static const String pause = 'assets/images/pause.svg';
  static const String edit_subtitle = 'assets/images/edit_subtitle.png';
  static const String only_lines_off = 'assets/images/only_lines_off.svg';
  static const String heart = 'assets/images/heart.svg';
}
// *********************************************************
