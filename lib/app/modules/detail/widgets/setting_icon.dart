import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 设置图标组件
/// 齿轮样式的设置图标，颜色可自定义
class SettingIcon extends StatelessWidget {
  final Color? color;
  final double size;

  const SettingIcon({
    Key? key,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: _SettingIconPainter(
        color: iconColor,
      ),
    );
  }
}

class _SettingIconPainter extends CustomPainter {
  final Color color;

  _SettingIconPainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    // 绘制齿轮的外圈
    final gearPath = Path();
    
    // 齿轮的主体圆形
    final center = Offset(12.0 * scaleX, 12.0 * scaleY);
    final outerRadius = 9.0 * scaleX;
    final innerRadius = 6.0 * scaleX;
    
    // 绘制齿轮的齿
    final teethCount = 8;
    final angleStep = (2 * 3.14159) / teethCount;
    
    for (int i = 0; i < teethCount; i++) {
      final angle = i * angleStep;
      final nextAngle = (i + 1) * angleStep;
      
      // 齿的外边缘
      final toothStart = Offset(
        center.dx + innerRadius * 1.2 * scaleX * cos(angle),
        center.dy + innerRadius * 1.2 * scaleY * sin(angle),
      );
      final toothEnd = Offset(
        center.dx + innerRadius * 1.2 * scaleX * cos(nextAngle),
        center.dy + innerRadius * 1.2 * scaleY * sin(nextAngle),
      );
      final toothTip = Offset(
        center.dx + outerRadius * cos(angle + angleStep / 2),
        center.dy + outerRadius * sin(angle + angleStep / 2),
      );
      
      if (i == 0) {
        gearPath.moveTo(toothStart.dx, toothStart.dy);
      }
      
      gearPath.lineTo(toothTip.dx, toothTip.dy);
      gearPath.lineTo(toothEnd.dx, toothEnd.dy);
    }
    gearPath.close();
    
    canvas.drawPath(gearPath, paint);

    // 绘制中心圆
    canvas.drawCircle(center, 3.0 * scaleX, paint);
  }

  // 辅助函数：计算余弦值
  double cos(double angle) {
    return math.cos(angle);
  }

  // 辅助函数：计算正弦值  
  double sin(double angle) {
    return math.sin(angle);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _SettingIconPainter ||
        oldDelegate.color != color;
  }
}
