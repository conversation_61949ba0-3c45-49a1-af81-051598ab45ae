import 'package:flutter/material.dart';

/// 编辑图标组件
/// 简单的编辑笔图标，颜色可自定义
class EditIcon extends StatelessWidget {
  final Color? color;
  final double size;

  const EditIcon({
    Key? key,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Si<PERSON>(size, size),
      painter: _EditIconPainter(
        color: iconColor,
      ),
    );
  }
}

class _EditIconPainter extends CustomPainter {
  final Color color;

  _EditIconPainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    // 绘制编辑笔的路径
    final editPath = Path();
    
    // 笔尖到笔身的主要路径
    editPath.moveTo(11.0 * scaleX, 4.0 * scaleY);
    editPath.lineTo(14.0 * scaleX, 7.0 * scaleY);
    editPath.lineTo(7.5 * scaleX, 13.5 * scaleY);
    editPath.lineTo(4.5 * scaleX, 16.5 * scaleY);
    editPath.lineTo(2.0 * scaleX, 22.0 * scaleY);
    editPath.lineTo(7.5 * scaleX, 19.5 * scaleY);
    editPath.lineTo(10.5 * scaleX, 16.5 * scaleY);
    editPath.lineTo(17.0 * scaleX, 10.0 * scaleY);
    editPath.lineTo(20.0 * scaleX, 7.0 * scaleY);
    editPath.lineTo(17.0 * scaleX, 4.0 * scaleY);
    editPath.lineTo(11.0 * scaleX, 4.0 * scaleY);
    
    // 笔尖部分
    editPath.moveTo(11.0 * scaleX, 4.0 * scaleY);
    editPath.lineTo(14.0 * scaleX, 7.0 * scaleY);
    
    // 编辑区域
    editPath.moveTo(7.5 * scaleX, 13.5 * scaleY);
    editPath.lineTo(10.5 * scaleX, 16.5 * scaleY);

    canvas.drawPath(editPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _EditIconPainter ||
        oldDelegate.color != color;
  }
}
