import 'package:flutter/material.dart';

/// AI图标组件
/// 简单的AI机器人图标，颜色可自定义
class AiIcon extends StatelessWidget {
  final Color? color;
  final double size;

  const AiIcon({
    Key? key,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Size(size, size),
      painter: _AiIconPainter(
        color: iconColor,
      ),
    );
  }
}

class _AiIconPainter extends CustomPainter {
  final Color color;

  _AiIconPainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    // 绘制AI机器人的头部（圆角矩形）
    final headRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(6.0 * scaleX, 6.0 * scaleY, 12.0 * scaleX, 10.0 * scaleY),
      Radius.circular(3.0 * scaleX),
    );
    canvas.drawRRect(headRect, paint);

    // 绘制天线
    final antennaPath = Path();
    antennaPath.moveTo(12.0 * scaleX, 6.0 * scaleY);
    antennaPath.lineTo(12.0 * scaleX, 3.0 * scaleY);
    canvas.drawPath(antennaPath, paint);
    
    // 天线顶部的小圆点
    canvas.drawCircle(Offset(12.0 * scaleX, 3.0 * scaleY), 1.0 * scaleX, fillPaint);

    // 绘制眼睛
    canvas.drawCircle(Offset(9.5 * scaleX, 10.0 * scaleY), 1.0 * scaleX, fillPaint);
    canvas.drawCircle(Offset(14.5 * scaleX, 10.0 * scaleY), 1.0 * scaleX, fillPaint);

    // 绘制嘴巴（小弧线）
    final mouthPath = Path();
    mouthPath.addArc(
      Rect.fromCenter(
        center: Offset(12.0 * scaleX, 13.0 * scaleY),
        width: 4.0 * scaleX,
        height: 2.0 * scaleY,
      ),
      0,
      3.14159, // π
    );
    canvas.drawPath(mouthPath, paint);

    // 绘制身体
    final bodyRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(8.0 * scaleX, 16.0 * scaleY, 8.0 * scaleX, 6.0 * scaleY),
      Radius.circular(2.0 * scaleX),
    );
    canvas.drawRRect(bodyRect, paint);

    // 绘制手臂
    final leftArmPath = Path();
    leftArmPath.moveTo(8.0 * scaleX, 18.0 * scaleY);
    leftArmPath.lineTo(5.0 * scaleX, 18.0 * scaleY);
    canvas.drawPath(leftArmPath, paint);

    final rightArmPath = Path();
    rightArmPath.moveTo(16.0 * scaleX, 18.0 * scaleY);
    rightArmPath.lineTo(19.0 * scaleX, 18.0 * scaleY);
    canvas.drawPath(rightArmPath, paint);

    // 绘制腿
    final leftLegPath = Path();
    leftLegPath.moveTo(10.0 * scaleX, 22.0 * scaleY);
    leftLegPath.lineTo(10.0 * scaleX, 24.0 * scaleY);
    canvas.drawPath(leftLegPath, paint);

    final rightLegPath = Path();
    rightLegPath.moveTo(14.0 * scaleX, 22.0 * scaleY);
    rightLegPath.lineTo(14.0 * scaleX, 24.0 * scaleY);
    canvas.drawPath(rightLegPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _AiIconPainter ||
        oldDelegate.color != color;
  }
}
