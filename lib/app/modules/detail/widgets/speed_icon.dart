import 'package:flutter/material.dart';

/// 播放速度图标组件
/// 支持1x和0.5x两种速度状态，颜色可自定义
class SpeedIcon extends StatelessWidget {
  final bool isHalfSpeed;
  final Color? color;
  final double size;

  const SpeedIcon({
    Key? key,
    required this.isHalfSpeed,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Size(size, size),
      painter: _SpeedIconPainter(
        isHalfSpeed: isHalfSpeed,
        color: iconColor,
      ),
    );
  }
}

class _SpeedIconPainter extends CustomPainter {
  final bool isHalfSpeed;
  final Color color;

  _SpeedIconPainter({
    required this.isHalfSpeed,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    // 绘制外框（YouTube播放器样式的矩形）
    _drawFrame(canvas, paint, scaleX, scaleY);
    
    // 绘制播放按钮的三角形
    _drawPlayButton(canvas, paint, scaleX, scaleY);
    
    if (isHalfSpeed) {
      // 绘制"0.5"文字
      _drawHalfSpeedText(canvas, fillPaint, scaleX, scaleY);
    } else {
      // 绘制"1"文字
      _drawNormalSpeedText(canvas, fillPaint, scaleX, scaleY);
    }
  }

  void _drawFrame(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    final framePath = Path();
    framePath.moveTo(22.5395 * scaleX, 5.46658 * scaleY);
    framePath.cubicTo(
      22.4207 * scaleX, 4.98285 * scaleY,
      22.1788 * scaleX, 4.53964 * scaleY,
      21.8381 * scaleX, 4.18172 * scaleY,
    );
    framePath.cubicTo(
      21.4975 * scaleX, 3.8238 * scaleY,
      21.0703 * scaleX, 3.56383 * scaleY,
      20.5996 * scaleX, 3.42808 * scaleY,
    );
    framePath.cubicTo(
      18.8796 * scaleX, 3.0 * scaleY,
      12.0 * scaleX, 3.0 * scaleY,
      12.0 * scaleX, 3.0 * scaleY,
    );
    framePath.cubicTo(
      12.0 * scaleX, 3.0 * scaleY,
      5.12036 * scaleX, 3.0 * scaleY,
      3.40045 * scaleX, 3.46885 * scaleY,
    );
    framePath.cubicTo(
      2.92972 * scaleX, 3.6046 * scaleY,
      2.50247 * scaleX, 3.86457 * scaleY,
      2.16186 * scaleX, 4.22249 * scaleY,
    );
    framePath.cubicTo(
      1.82124 * scaleX, 4.58041 * scaleY,
      1.57933 * scaleX, 5.02362 * scaleY,
      1.46055 * scaleX, 5.50735 * scaleY,
    );
    framePath.cubicTo(
      1.14578 * scaleX, 7.2865 * scaleY,
      0.991808 * scaleX, 10.2126 * scaleY,
      1.00057 * scaleX, 12.0204 * scaleY,
    );
    framePath.cubicTo(
      0.989351 * scaleX, 13.8418 * scaleY,
      1.14333 * scaleX, 16.7817 * scaleY,
      1.46055 * scaleX, 18.5742 * scaleY,
    );
    framePath.cubicTo(
      1.5915 * scaleX, 19.0429 * scaleY,
      1.83883 * scaleX, 19.4692 * scaleY,
      2.17865 * scaleX, 19.8121 * scaleY,
    );
    framePath.cubicTo(
      2.51847 * scaleX, 20.1549 * scaleY,
      2.93929 * scaleX, 20.4025 * scaleY,
      3.40045 * scaleX, 20.5312 * scaleY,
    );
    framePath.cubicTo(
      5.12036 * scaleX, 21.0 * scaleY,
      8.63686 * scaleX, 21.0 * scaleY,
      12.0 * scaleX, 21.0 * scaleY,
    );
    framePath.cubicTo(
      15.3631 * scaleX, 21.0 * scaleY,
      18.8796 * scaleX, 21.0 * scaleY,
      20.5996 * scaleX, 20.5312 * scaleY,
    );
    framePath.cubicTo(
      21.0703 * scaleX, 20.3954 * scaleY,
      21.4975 * scaleX, 20.1354 * scaleY,
      21.8381 * scaleX, 19.7775 * scaleY,
    );
    framePath.cubicTo(
      22.1788 * scaleX, 19.4196 * scaleY,
      22.4207 * scaleX, 18.9764 * scaleY,
      22.5395 * scaleX, 18.4927 * scaleY,
    );
    framePath.cubicTo(
      22.8518 * scaleX, 16.7269 * scaleY,
      23.0058 * scaleX, 13.8146 * scaleY,
      22.9994 * scaleX, 12.0204 * scaleY,
    );
    framePath.cubicTo(
      23.0106 * scaleX, 10.1989 * scaleY,
      22.8567 * scaleX, 7.25912 * scaleY,
      22.5395 * scaleX, 5.46658 * scaleY,
    );
    framePath.close();

    canvas.drawPath(framePath, paint);
  }

  void _drawPlayButton(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    paint.strokeWidth = 1.42857;
    
    // 绘制X形状的播放按钮
    final line1Path = Path();
    line1Path.moveTo(17.3027 * scaleX, 10.2678 * scaleY);
    line1Path.lineTo(13.7672 * scaleX, 13.8034 * scaleY);
    
    final line2Path = Path();
    line2Path.moveTo(17.3027 * scaleX, 13.8032 * scaleY);
    line2Path.lineTo(13.7672 * scaleX, 10.2677 * scaleY);
    
    canvas.drawPath(line1Path, paint);
    canvas.drawPath(line2Path, paint);
    
    paint.strokeWidth = 2.0; // 恢复原始线宽
  }

  void _drawNormalSpeedText(Canvas canvas, Paint fillPaint, double scaleX, double scaleY) {
    // 绘制数字"1"
    final textPath = Path();
    textPath.moveTo(5.72852 * scaleX, 15.6816 * scaleY);
    textPath.lineTo(7.61084 * scaleX, 15.6816 * scaleY);
    textPath.lineTo(7.61084 * scaleX, 10.2324 * scaleY);
    // 继续添加"1"的路径...
    
    canvas.drawPath(textPath, fillPaint);
  }

  void _drawHalfSpeedText(Canvas canvas, Paint fillPaint, double scaleX, double scaleY) {
    // 绘制"0.5"文字 - 这里简化处理，实际应该绘制完整的路径
    final textPaint = TextPainter(
      text: TextSpan(
        text: '0.5',
        style: TextStyle(
          color: color,
          fontSize: 8.0 * scaleX,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPaint.layout();
    textPaint.paint(
      canvas,
      Offset(
        (24.0 * scaleX - textPaint.width) / 2,
        (24.0 * scaleY - textPaint.height) / 2 + 2.0 * scaleY,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _SpeedIconPainter ||
        oldDelegate.isHalfSpeed != isHalfSpeed ||
        oldDelegate.color != color;
  }
}
