import 'package:flutter/material.dart';

/// 翻译模式枚举
enum TranslateMode {
  both,    // 双语模式
  native,  // 原文模式
  target,  // 译文模式
}

/// 翻译图标组件
/// 支持三种翻译模式，颜色可自定义
class TranslateIcon extends StatelessWidget {
  final TranslateMode mode;
  final Color? color;
  final double size;

  const TranslateIcon({
    Key? key,
    required this.mode,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Size(size, size),
      painter: _TranslateIconPainter(
        mode: mode,
        color: iconColor,
      ),
    );
  }
}

class _TranslateIconPainter extends CustomPainter {
  final TranslateMode mode;
  final Color color;

  _TranslateIconPainter({
    required this.mode,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.66667
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // 调整比例，因为原SVG是20x20的
    final scaleX = size.width / 20.0;
    final scaleY = size.height / 20.0;

    // 绘制右侧的"A"字母部分（译文）
    _drawTargetText(canvas, paint, scaleX, scaleY, mode == TranslateMode.native);
    
    // 绘制左侧的中文部分（原文）
    _drawNativeText(canvas, paint, scaleX, scaleY, mode == TranslateMode.target);
  }

  void _drawTargetText(Canvas canvas, Paint paint, double scaleX, double scaleY, bool isOpaque) {
    if (isOpaque) {
      paint.color = color.withOpacity(0.3);
    } else {
      paint.color = color;
    }

    final targetPath = Path();
    
    // "A"字母的路径
    targetPath.moveTo(10.7612 * scaleX, 14.1667 * scaleY);
    targetPath.lineTo(16.7395 * scaleX, 14.1667 * scaleY);
    
    targetPath.moveTo(10.7612 * scaleX, 14.1667 * scaleY);
    targetPath.lineTo(9.16699 * scaleX, 17.5 * scaleY);
    
    targetPath.moveTo(10.7612 * scaleX, 14.1667 * scaleY);
    targetPath.lineTo(13.1489 * scaleX, 9.17419 * scaleY);
    targetPath.cubicTo(
      13.3413 * scaleX, 8.77189 * scaleY,
      13.4375 * scaleX, 8.57075 * scaleY,
      13.5691 * scaleX, 8.50718 * scaleY,
    );
    targetPath.cubicTo(
      13.6836 * scaleX, 8.4519 * scaleY,
      13.817 * scaleX, 8.4519 * scaleY,
      13.9315 * scaleX, 8.50718 * scaleY,
    );
    targetPath.cubicTo(
      14.0631 * scaleX, 8.57075 * scaleY,
      14.1593 * scaleX, 8.77189 * scaleY,
      14.3517 * scaleX, 9.17419 * scaleY,
    );
    targetPath.lineTo(16.7395 * scaleX, 14.1667 * scaleY);
    
    targetPath.moveTo(16.7395 * scaleX, 14.1667 * scaleY);
    targetPath.lineTo(18.3337 * scaleX, 17.5 * scaleY);

    canvas.drawPath(targetPath, paint);
  }

  void _drawNativeText(Canvas canvas, Paint paint, double scaleX, double scaleY, bool isOpaque) {
    if (isOpaque) {
      paint.color = color.withOpacity(0.3);
    } else {
      paint.color = color;
    }

    final nativePath = Path();
    
    // 中文字符的路径
    nativePath.moveTo(1.66699 * scaleX, 4.16667 * scaleY);
    nativePath.lineTo(6.66699 * scaleX, 4.16667 * scaleY);
    
    nativePath.moveTo(6.66699 * scaleX, 4.16667 * scaleY);
    nativePath.lineTo(9.58366 * scaleX, 4.16667 * scaleY);
    
    nativePath.moveTo(6.66699 * scaleX, 4.16667 * scaleY);
    nativePath.lineTo(6.66699 * scaleX, 2.5 * scaleY);
    
    nativePath.moveTo(9.58366 * scaleX, 4.16667 * scaleY);
    nativePath.lineTo(11.667 * scaleX, 4.16667 * scaleY);
    
    nativePath.moveTo(9.58366 * scaleX, 4.16667 * scaleY);
    nativePath.cubicTo(
      9.17021 * scaleX, 6.63107 * scaleY,
      8.21081 * scaleX, 8.86349 * scaleY,
      6.80495 * scaleX, 10.737 * scaleY,
    );
    
    nativePath.moveTo(8.33366 * scaleX, 11.6667 * scaleY);
    nativePath.cubicTo(
      7.82322 * scaleX, 11.4373 * scaleY,
      7.30253 * scaleX, 11.1184 * scaleY,
      6.80495 * scaleX, 10.737 * scaleY,
    );
    
    nativePath.moveTo(6.80495 * scaleX, 10.737 * scaleY);
    nativePath.cubicTo(
      5.67784 * scaleX, 9.87314 * scaleY,
      4.66929 * scaleX, 8.68886 * scaleY,
      4.16699 * scaleX, 7.5 * scaleY,
    );
    
    nativePath.moveTo(6.80495 * scaleX, 10.737 * scaleY);
    nativePath.cubicTo(
      5.46771 * scaleX, 12.5191 * scaleY,
      3.72652 * scaleX, 13.9765 * scaleY,
      1.66699 * scaleX, 15.0 * scaleY,
    );

    canvas.drawPath(nativePath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _TranslateIconPainter ||
        oldDelegate.mode != mode ||
        oldDelegate.color != color;
  }
}
