import 'package:flutter/material.dart';

/// 眼睛图标组件
/// 支持睁眼/闭眼两种状态，颜色可自定义
class EyeIcon extends StatelessWidget {
  final bool isOpen;
  final Color? color;
  final double size;

  const EyeIcon({
    Key? key,
    required this.isOpen,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: _EyeIconPainter(
        isOpen: isOpen,
        color: iconColor,
      ),
    );
  }
}

class _EyeIconPainter extends CustomPainter {
  final bool isOpen;
  final Color color;

  _EyeIconPainter({
    required this.isOpen,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    if (isOpen) {
      // 绘制睁开的眼睛
      _drawOpenEye(canvas, paint, scaleX, scaleY);
    } else {
      // 绘制闭上的眼睛
      _drawClosedEye(canvas, paint, scaleX, scaleY);
    }
  }

  void _drawOpenEye(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    // 外眼轮廓路径
    final eyePath = Path();
    eyePath.moveTo(2.42012 * scaleX, 12.7132 * scaleY);
    eyePath.cubicTo(
      2.28394 * scaleX, 12.4975 * scaleY,
      2.21584 * scaleX, 12.3897 * scaleY,
      2.17772 * scaleX, 12.2234 * scaleY,
    );
    eyePath.cubicTo(
      2.14909 * scaleX, 12.0985 * scaleY,
      2.14909 * scaleX, 11.9015 * scaleY,
      2.17772 * scaleX, 11.7766 * scaleY,
    );
    eyePath.cubicTo(
      2.21584 * scaleX, 11.6103 * scaleY,
      2.28394 * scaleX, 11.5025 * scaleY,
      2.42012 * scaleX, 11.2868 * scaleY,
    );
    eyePath.cubicTo(
      3.54553 * scaleX, 9.50484 * scaleY,
      6.8954 * scaleX, 5.0 * scaleY,
      12.0004 * scaleX, 5.0 * scaleY,
    );
    eyePath.cubicTo(
      17.1054 * scaleX, 5.0 * scaleY,
      20.4553 * scaleX, 9.50484 * scaleY,
      21.5807 * scaleX, 11.2868 * scaleY,
    );
    eyePath.cubicTo(
      21.7169 * scaleX, 11.5025 * scaleY,
      21.785 * scaleX, 11.6103 * scaleY,
      21.8231 * scaleX, 11.7766 * scaleY,
    );
    eyePath.cubicTo(
      21.8517 * scaleX, 11.9015 * scaleY,
      21.8517 * scaleX, 12.0985 * scaleY,
      21.8231 * scaleX, 12.2234 * scaleY,
    );
    eyePath.cubicTo(
      21.785 * scaleX, 12.3897 * scaleY,
      21.7169 * scaleX, 12.4975 * scaleY,
      21.5807 * scaleX, 12.7132 * scaleY,
    );
    eyePath.cubicTo(
      20.4553 * scaleX, 14.4952 * scaleY,
      17.1054 * scaleX, 19.0 * scaleY,
      12.0004 * scaleX, 19.0 * scaleY,
    );
    eyePath.cubicTo(
      6.8954 * scaleX, 19.0 * scaleY,
      3.54553 * scaleX, 14.4952 * scaleY,
      2.42012 * scaleX, 12.7132 * scaleY,
    );
    
    canvas.drawPath(eyePath, paint);

    // 瞳孔
    canvas.drawCircle(
      Offset(12.0004 * scaleX, 12.0 * scaleY),
      3.0 * scaleX,
      paint,
    );
  }

  void _drawClosedEye(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    // 复杂的闭眼路径
    final closedEyePath = Path();
    
    // 第一段路径
    closedEyePath.moveTo(10.7429 * scaleX, 5.09232 * scaleY);
    closedEyePath.cubicTo(
      11.1494 * scaleX, 5.03223 * scaleY,
      11.5686 * scaleX, 5.0 * scaleY,
      12.0004 * scaleX, 5.0 * scaleY,
    );
    closedEyePath.cubicTo(
      17.1054 * scaleX, 5.0 * scaleY,
      20.4553 * scaleX, 9.50484 * scaleY,
      21.5807 * scaleX, 11.2868 * scaleY,
    );
    
    // 继续添加其他路径段...
    closedEyePath.moveTo(6.72432 * scaleX, 6.71504 * scaleY);
    closedEyePath.cubicTo(
      4.56225 * scaleX, 8.1817 * scaleY,
      3.09445 * scaleX, 10.2194 * scaleY,
      2.42111 * scaleX, 11.2853 * scaleY,
    );
    
    // 斜线（表示闭眼）
    closedEyePath.moveTo(3.00042 * scaleX, 3.0 * scaleY);
    closedEyePath.lineTo(21.0004 * scaleX, 21.0 * scaleY);
    
    // 部分瞳孔区域
    closedEyePath.moveTo(9.8791 * scaleX, 9.87868 * scaleY);
    closedEyePath.cubicTo(
      9.3362 * scaleX, 10.4216 * scaleY,
      9.00042 * scaleX, 11.1716 * scaleY,
      9.00042 * scaleX, 12.0 * scaleY,
    );
    closedEyePath.cubicTo(
      9.00042 * scaleX, 13.6569 * scaleY,
      10.3436 * scaleX, 15.0 * scaleY,
      12.0004 * scaleX, 15.0 * scaleY,
    );
    closedEyePath.cubicTo(
      12.8288 * scaleX, 15.0 * scaleY,
      13.5788 * scaleX, 14.6642 * scaleY,
      14.1217 * scaleX, 14.1213 * scaleY,
    );

    canvas.drawPath(closedEyePath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _EyeIconPainter ||
        oldDelegate.isOpen != isOpen ||
        oldDelegate.color != color;
  }
}
