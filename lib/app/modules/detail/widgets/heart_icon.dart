import 'package:flutter/material.dart';

/// 心形收藏图标组件
/// 支持收藏/未收藏两种状态，颜色可自定义
class HeartIcon extends StatelessWidget {
  final bool isFavorited;
  final Color? color;
  final double size;

  const HeartIcon({
    Key? key,
    required this.isFavorited,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Size(size, size),
      painter: _HeartIconPainter(
        isFavorited: isFavorited,
        color: iconColor,
      ),
    );
  }
}

class _HeartIconPainter extends CustomPainter {
  final bool isFavorited;
  final Color color;

  _HeartIconPainter({
    required this.isFavorited,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    if (isFavorited) {
      // 绘制填充的心形（已收藏状态）
      _drawFilledHeart(canvas, paint, scaleX, scaleY);
    } else {
      // 绘制空心的心形（未收藏状态）
      _drawOutlineHeart(canvas, paint, scaleX, scaleY);
    }
  }

  void _drawFilledHeart(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    paint.style = PaintingStyle.fill;
    
    final heartPath = Path();
    heartPath.moveTo(16.1111 * scaleX, 3.0 * scaleY);
    heartPath.cubicTo(
      19.6333 * scaleX, 3.0 * scaleY,
      22.0 * scaleX, 6.3525 * scaleY,
      22.0 * scaleX, 9.48 * scaleY,
    );
    heartPath.cubicTo(
      22.0 * scaleX, 15.8138 * scaleY,
      12.1778 * scaleX, 21.0 * scaleY,
      12.0 * scaleX, 21.0 * scaleY,
    );
    heartPath.cubicTo(
      11.8222 * scaleX, 21.0 * scaleY,
      2.0 * scaleX, 15.8138 * scaleY,
      2.0 * scaleX, 9.48 * scaleY,
    );
    heartPath.cubicTo(
      2.0 * scaleX, 6.3525 * scaleY,
      4.36667 * scaleX, 3.0 * scaleY,
      7.88889 * scaleX, 3.0 * scaleY,
    );
    heartPath.cubicTo(
      9.91111 * scaleX, 3.0 * scaleY,
      11.2333 * scaleX, 4.02375 * scaleY,
      12.0 * scaleX, 4.92375 * scaleY,
    );
    heartPath.cubicTo(
      12.7667 * scaleX, 4.02375 * scaleY,
      14.0889 * scaleX, 3.0 * scaleY,
      16.1111 * scaleX, 3.0 * scaleY,
    );
    heartPath.close();

    canvas.drawPath(heartPath, paint);
    
    // 绘制描边
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(heartPath, paint);
  }

  void _drawOutlineHeart(Canvas canvas, Paint paint, double scaleX, double scaleY) {
    paint.style = PaintingStyle.stroke;
    
    final heartPath = Path();
    heartPath.moveTo(15.9056 * scaleX, 3.44995 * scaleY);
    heartPath.cubicTo(
      19.2517 * scaleX, 3.44995 * scaleY,
      21.5 * scaleX, 6.63483 * scaleY,
      21.5 * scaleX, 9.60595 * scaleY,
    );
    heartPath.cubicTo(
      21.5 * scaleX, 15.623 * scaleY,
      12.1689 * scaleX, 20.55 * scaleY,
      12.0 * scaleX, 20.55 * scaleY,
    );
    heartPath.cubicTo(
      11.8311 * scaleX, 20.55 * scaleY,
      2.5 * scaleX, 15.623 * scaleY,
      2.5 * scaleX, 9.60595 * scaleY,
    );
    heartPath.cubicTo(
      2.5 * scaleX, 6.63483 * scaleY,
      4.74833 * scaleX, 3.44995 * scaleY,
      8.09444 * scaleX, 3.44995 * scaleY,
    );
    heartPath.cubicTo(
      10.0156 * scaleX, 3.44995 * scaleY,
      11.2717 * scaleX, 4.42251 * scaleY,
      12.0 * scaleX, 5.27751 * scaleY,
    );
    heartPath.cubicTo(
      12.7283 * scaleX, 4.42251 * scaleY,
      13.9844 * scaleX, 3.44995 * scaleY,
      15.9056 * scaleX, 3.44995 * scaleY,
    );
    heartPath.close();

    canvas.drawPath(heartPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _HeartIconPainter ||
        oldDelegate.isFavorited != isFavorited ||
        oldDelegate.color != color;
  }
}
