import 'dart:io';
import 'package:flutter/material.dart';
import 'package:focus_detector/focus_detector.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/views/ls_mode_setting_view.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/widgets/nest_pageview.dart';
import 'package:lsenglish/widgets/split_english.dart';
import 'package:lsenglish/video/video_controls_material.dart';
import 'package:lsenglish/widgets/wave.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import '../controllers/detail_controller.dart';
import '../widgets/record_icon.dart';
import 'ai_chat_widget.dart';
import 'landscape.dart';
import 'player_drag_slider.dart';
import 'title_more_widget.dart';
import 'package:lsenglish/utils/player_menu.dart';

const videoRatio = 9 / 16;

class DetailView extends GetView<DetailController> {
  const DetailView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return FocusDetector(
      onVisibilityLost: () {
        if (Get.isRegistered<DetailController>()) {
          controller.onVisibilityLost();
        }
      },
      onVisibilityGained: () {
        controller.onVisibilityGained();
      },
      child: OrientationBuilder(
        builder: (BuildContext context, Orientation orientation) {
          if (controller.isLandscape.value) {
            return _buildLandscapeVideo();
          }
          return Scaffold(
            backgroundColor: Get.theme.scaffoldBackgroundColor,
            body: Obx(() => Column(
                  children: [
                    Obx(() => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: controller.isAppBarVisible.value ? 100 : 56,
                          child: AppBar(
                            backgroundColor: Get.theme.scaffoldBackgroundColor,
                            actions: [
                              GestureDetector(
                                  onTap: () => Get.to(() => const LsModeSettingWidget()),
                                  child: const Text("LS", style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18))),
                              Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: _buildMoreWidget(),
                              ),
                            ],
                          ),
                        )),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Container(
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: const Color(0xffEFEFF0),
                          ),
                          child: _buildPortraitVideo()),
                    ),
                    Gap(6.whs),
                    Obx(() => Expanded(
                            child: Stack(
                          children: [
                            //这里必须要用Stack，这样pagecontroler可以先渲染 不然后面更新渲染会导致pagecontroller没有被附加到view上
                            controller.videoKit.openLsMode.value ? _buildLsModeSubtitles() : _buildOnlyListenSubtitles(),
                            _buildLoadingSubtitle(),
                          ],
                        ))),
                    if (!controller.loadingSubtitle.value) _buildPortraitVoice(),
                    Gap(8.whs),
                    SizedBox(height: 55.whs, child: _buildVoiceScore()),
                    Gap(8.whs),
                    Obx(() => DualSlider(
                          ratio: controller.seekBarRatio.value,
                          onDragEnd: (double ratio) {
                            controller.onSliderChange(ratio);
                          },
                          totalDuration: controller.totalDuration.value,
                        )),
                    _buildPortraitLsModeControl(),
                    Gap(Platform.isMacOS ? 8 : 30),
                  ],
                )),
          );
        },
      ),
    );
  }

  Widget _buildLoadingSubtitle() {
    return controller.loadingSubtitle.value
        ? const Positioned.fill(child: Center(child: CircularProgressIndicator()))
        : controller.videoKit.subtitles.isEmpty
            ? _buildNoSubtitle()
            : const SizedBox.shrink();
  }

  Widget _buildNoSubtitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text("暂无字幕,请导入"),
        const Gap(32),
        Column(
          children: [
            FilledButton(
              onPressed: () {
                controller.goSubtitleSearch();
              },
              child: const Text("去导入"),
            ),
            const Gap(20),
            FilledButton(
              onPressed: () {
                controller.goAIGenSubtitle();
              },
              child: const Text("AI生成字幕"),
            ),
            const Gap(20),
            FilledButton(
              onPressed: () {
                controller.getSubtitleByVideo();
              },
              child: const Text("获取自带字幕"),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPortraitVideo() {
    return GestureDetector(
      onTap: () {
        controller.videoKit.toggleFullscreen();
      },
      child: AspectRatio(
        aspectRatio: (1 / videoRatio),
        child: Stack(
          children: [
            MyVideoControlsTheme(
              normal: kDefaultMyVideoControlsThemeData,
              local: controller.isLocalVideo ? kDefaultLocalVideoControlsThemeData : null,
              videoControlsCallbacks: controller.videoControlsCallbacks,
              child: Video(
                controller: controller.videoController,
                fit: BoxFit.cover,
                controls: NoVideoControls,
                pauseUponEnteringBackgroundMode: true,
                resumeUponEnteringForegroundMode: controller.videoKit.onlyPlayLines.value,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLandscapeVideo() {
    return Container(
      color: Colors.black,
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(30)),
                child: Stack(
                  children: [
                    MyVideoControlsTheme(
                      normal: kDefaultMyVideoControlsThemeData,
                      local: controller.isLocalVideo ? kDefaultLocalVideoControlsThemeData : null,
                      child: Video(
                        controller: controller.videoController,
                        fit: BoxFit.cover,
                        controls: MyPhoneVideoControls,
                        pauseUponEnteringBackgroundMode: true,
                        resumeUponEnteringForegroundMode: controller.videoKit.onlyPlayLines.value,
                      ),
                    ),
                    Positioned.fill(
                      child: TapJumpSubtitleWidget(
                        tapJumpSubtitleCallback: (bool isForward) {
                          if (isForward) {
                            controller.jumpNextSubtitle();
                          } else {
                            controller.jumpPreSubtitle();
                          }
                        },
                      ),
                    ),
                    Obx(() => Visibility(
                          visible: controller.videoKit.subtitles.isNotEmpty && !controller.showSubtitlePlaceholder.value,
                          child: Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: ConstrainedBox(
                              constraints: BoxConstraints(maxHeight: Get.height * 0.5),
                              child: ClipRRect(
                                child: Stack(
                                  children: [
                                    SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                                        child: Obx(() => Center(
                                              child: Container(
                                                padding: EdgeInsets.symmetric(horizontal: 8.whs, vertical: 4.whs),
                                                decoration: BoxDecoration(
                                                  color: Colors.black.withValues(alpha: 0.6),
                                                  borderRadius: BorderRadius.circular(4.whs),
                                                ),
                                                child: SplitEnglishWidget(
                                                  // recordText: controller.recordText.value,
                                                  isLandscape: true,
                                                  showPlaceholder: false,
                                                  centerText: true,
                                                  subtitle: controller.videoKit.subtitles[controller.videoKit.currentSubtitleIndex.value],
                                                  subtitleMode: controller.videoKit.currentSubtitleMode.value,
                                                  evalResult:
                                                      controller.speechEvaluationMap[controller.videoKit.currentSubtitleIndex.value]?.evalResult,
                                                ),
                                              ),
                                            )),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )),
                    Positioned(
                      child: _buildLandscapeTitlebar(),
                    ),
                    Positioned(
                      top: 6.whs,
                      left: 0,
                      right: 0,
                      child: Obx(() => AnimatedOpacity(
                            duration: const Duration(milliseconds: 300),
                            opacity: controller.showCurrentLandScapeToast.value ? 1.0 : 0.0,
                            child: IntrinsicWidth(
                              child: Center(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.7),
                                    borderRadius: BorderRadius.all(Radius.circular(8.whs)),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  child: Text(
                                    controller.currentToast.value,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            ),
                          )),
                    )
                  ],
                ),
              ),
            ),
          ),
          Row(
            children: [
              _buildLandscapeVoice(),
              SizedBox(
                height: Get.height,
                child: _buildLandscapeLsModeControl(),
              ),
              const Gap(16),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLsModeSubtitles() {
    return NestPageView(
        pageController: controller.pageController,
        onUserScrollEnd: () => controller.onUserScrollEnd(),
        onUserScrollStart: () => controller.onUserScrollStart(),
        onPageScrollDirection: (isForward) => controller.onPageScrollDirection(isForward),
        builder: (BuildContext context, PageController pageController, ScrollController scrollController, int pageIndex) {
          return Obx(() => PageView.builder(
                //屏蔽默认的滑动响应
                // physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (value) => controller.onPageChanged(value),
                controller: pageController,
                scrollDirection: Axis.horizontal,
                itemCount: controller.videoKit.subtitles.length,
                itemBuilder: (BuildContext context, int index) {
                  return GestureDetector(
                    onTap: () => controller.lsContainerClick(index),
                    onDoubleTap: () {
                      controller.toggleSubtitlePlaceholder();
                    },
                    // child: ScrollConfiguration(
                    //   behavior: ScrollConfiguration.of(context).copyWith(overscroll: false),
                    //   child: SingleChildScrollView(
                    //     physics: const NeverScrollableScrollPhysics(),
                    //     controller: scrollController,
                    //     child: Container(
                    //       key: PageStorageKey(index),
                    //       child: _buildLsPageChild(context, index),
                    //     ),
                    //   ),
                    // ),
                    child: SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      controller: scrollController,
                      child: Container(
                        key: PageStorageKey(index),
                        child: _buildLsPageChild(context, index),
                      ),
                    ),
                  );
                },
              ));
        });
  }

  Widget _buildLsPageChild(BuildContext context, int index) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Column(
        children: [
          // Text("Index: $index ${controller.videoKit.subtitles[index].subtitleIndex}"),
          // Obx(() => Text("字幕序号: ${controller.videoKit.subtitles[index].subtitleIndex}")),
          // Obx(() => Text("时间: ${controller.videoKit.subtitles[index].start.inMilliseconds}  --- ${controller.videoKit.subtitles[index].end.inMilliseconds} ")),
          // Obx(() => Text(
          //     "时间: ${controller.videoKit.subtitles[index].start.inMilliseconds}  --- ${controller.videoKit.subtitles[index].end.inMilliseconds} ")),

          Container(
            color: Colors.transparent,
            child: Obx(
              () => SplitEnglishWidget(
                centerText: true,
                showPlaceholder: controller.showSubtitlePlaceholder.value,
                subtitle: controller.videoKit.subtitles[index],
                subtitleMode: controller.videoKit.currentSubtitleMode.value,
                evalResult: controller.speechEvaluationMap[index]?.evalResult,
                onPlaceholderTap: () {
                  controller.toggleSubtitlePlaceholder();
                },
              ),
            ),
          ),
          //笔记
          Obx(() => Visibility(
                visible: (controller.notesMap[controller.videoKit.subtitles[index].subtitleIndex]?.content ?? "") != "",
                child: IntrinsicHeight(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxHeight: 200),
                    child: GestureDetector(
                      onTap: () => controller.showAddNoteDialog(),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Container(
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                            color: controller.showSubtitlePlaceholder.value ? const Color(0xFFF6F6F6).withOpacity(0.5) : const Color(0xFFF6F6F6),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Container(
                                width: 4,
                                height: double.infinity,
                                decoration: BoxDecoration(
                                  color: controller.showSubtitlePlaceholder.value ? Get.theme.primaryColor.withOpacity(0.5) : Get.theme.primaryColor,
                                ),
                              ),
                              const Gap(8),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(6),
                                  child: Text(
                                    !controller.showSubtitlePlaceholder.value
                                        ? controller.notesMap[controller.videoKit.subtitles[index].subtitleIndex]?.content ?? ""
                                        : "\n\n\n",
                                    overflow: TextOverflow.fade,
                                    softWrap: true,
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildMoreWidget({Color? color}) {
    var titles = ["新增LS完成遍数", controller.videoKit.openLsMode.value ? "切换到仅听模式" : "切换到LS模式"];
    var icons = [R.plus, R.alignleft];
    return TitleMoreWidget(
      titles: titles,
      icons: icons,
      titleMoreCallback: (index) {
        if (index == 0) {
          controller.showLsTimeDialog();
        } else if (index == 1) {
          if (controller.videoKit.openLsMode.value) {
            controller.closeLsMode();
          } else {
            controller.openLsMode();
          }
        }
      },
      color: color,
    );
  }

  Widget _buildPortraitVoice() {
    return Obx(() => Visibility(
          visible: controller.videoKit.subtitles.isNotEmpty,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildVoice(),
              ),
              // _buildPortraitRecordingWidget(),
            ],
          ),
        ));
  }

  Widget _buildVoiceScore() {
    return Obx(() {
      // 如果正在录音，显示波形动画
      if (controller.recordingInLsMode.value) {
        return WaveformWidget(
          barCount: 30,
          barWidth: 2,
          barSpacing: 4,
          animationDuration: 500,
          delayBetweenBars: 100,
          minHeight: 25,
          maxHeight: 35,
          randomizeDuration: 300,
          random: false,
          barColor: Get.theme.primaryColor,
        );
      }

      final evalResult = controller.speechEvaluationMap[controller.currentPage.value]?.evalResult;

      // 如果没有评分结果，显示空状态
      if (evalResult == null) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Container(
            decoration: const BoxDecoration(
              color: Color(0xFFF5F5F5),
              borderRadius: BorderRadius.all(Radius.circular(100)),
            ),
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Center(
                child: Text(
                  "暂无录制",
                  style: TextStyle(
                    color: Color(0x80000000), // 50% 黑色
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ),
        );
      }

      final score = SpeechEvaluation.instance.getScore(evalResult);

      // 根据分数设置颜色
      Color backgroundColor;
      Color textColor;
      String scoreDesc;

      if (score >= 80) {
        backgroundColor = const Color(0xFFDAECDF);
        textColor = const Color(0xFF009951);
        scoreDesc = "优秀";
      } else if (score >= 60) {
        backgroundColor = const Color(0xFFFFF1C2);
        textColor = const Color(0xFFB86200);
        scoreDesc = "良好";
      } else {
        backgroundColor = const Color(0xFFFFE2E0);
        textColor = const Color(0xFFDC3412);
        scoreDesc = "较差";
      }

      // 否则显示评分结果
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: GestureDetector(
          onTap: controller.playRecord,
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.all(Radius.circular(100)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Gap(12.whs),
                    ImageLoader(R.voice_wave, color: textColor),
                    const Gap(4),
                    Text(
                      score.toString(),
                      style: Get.textTheme.labelLarge?.copyWith(color: textColor, fontWeight: FontWeight.bold, letterSpacing: 0),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      scoreDesc,
                      style: Get.textTheme.labelLarge?.copyWith(color: textColor),
                    ),
                    Gap(4.whs),
                    Icon(Icons.arrow_forward_ios, color: textColor, size: 16.whs),
                    Gap(12.whs),
                  ],
                )
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildLandscapeVoice() {
    return Obx(() => Visibility(
          visible: controller.videoKit.subtitles.isNotEmpty,
          child: Stack(
            children: [
              Center(
                child: Visibility(
                  visible: !controller.recordingInLsMode.value,
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: _buildVoice(isDark: true, isLandscape: true),
                  ),
                ),
              ),
              Center(child: _buildLandscapeRecordingWidget()),
            ],
          ),
        ));
  }

  Widget _buildLandscapeTitlebar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(onTap: () => controller.backWhenFullscreen(), child: ImageLoader(R.fullscreen_close)),
          const Gap(8),
          const Spacer(),
          // _buildSerialNumber(isDark: true),
          // _buildSwitchSubtitleVisibility(color: Colors.white),
          // _buildSwitchSubtitleMode(color: Colors.white),
          // _buildMoreWidget(color: Colors.white),
        ],
      ),
    );
  }

  List<Widget> _buildVoice({bool isDark = false, bool isLandscape = false}) {
    var iconSize = 72.whs;
    return [
      GestureDetector(
        onTap: controller.jumpPreSubtitle,
        child: Container(
          width: iconSize,
          height: iconSize,
          color: Colors.transparent,
        ),
      ),
      // GestureDetector(
      //   onTap: controller.lsPlayClick,
      //   child: Container(
      //     width: iconSize,
      //     height: iconSize,
      //     decoration: BoxDecoration(
      //       color: isLandscape ? Colors.white.withValues(alpha: 0.12) : const Color(0xffF2F4F7),
      //       shape: BoxShape.circle,
      //     ),
      //     child: Obx(() => ImageLoader(
      //           controller.videoKit.playing.value ? R.pause : R.play,
      //           size: 30,
      //           color: isLandscape ? Colors.white : const Color(0xff475467),
      //         )),
      //   ),
      // ),
      const Gap(24),
      // GestureDetector(
      //   onTap: controller.recordStart,
      //   child: Container(
      //     width: iconSize,
      //     height: iconSize,
      //     decoration: BoxDecoration(
      //       color: Get.theme.primaryColor,
      //       shape: BoxShape.circle,
      //     ),
      //     child: ImageLoader(R.record, color: Colors.white),
      //   ),
      // ),
      RecordIcon(
        color: Get.theme.primaryColor,
        size: iconSize,
        animationDuration: const Duration(milliseconds: 200),
        onRecordingStateChanged: (bool isRecording) async {
          await Future.delayed(const Duration(milliseconds: 200));
          if (isRecording) {
            controller.recordStart();
          } else {
            controller.recordStop();
          }
        },
      ),
      const Gap(24),
      // Stack(
      //   children: [
      //     GestureDetector(
      //       onTap: controller.playRecord,
      //       child: Container(
      //         width: iconSize,
      //         height: iconSize,
      //         decoration: BoxDecoration(
      //           color: isLandscape ? Colors.white.withValues(alpha: 0.12) : const Color(0xffF2F4F7),
      //           shape: BoxShape.circle,
      //         ),
      //         child: Obx(() {
      //           final evalResult = controller.speechEvaluationMap[controller.currentPage.value]?.evalResult;
      //           return ImageLoader(
      //             R.recording,
      //             color: (evalResult?.audioUrl?.isNotEmpty ?? false
      //                 ? (isLandscape ? Colors.white : const Color(0xff475467))
      //                 : (isLandscape ? Colors.white.withValues(alpha: 0.3) : const Color(0xff475467).withValues(alpha: 0.3))),
      //           );
      //         }),
      //       ),
      //     ),
      //     //评分
      //     Positioned(
      //       right: 0,
      //       top: 0,
      //       child: Transform.translate(
      //         offset: const Offset(5, -5),
      //         child: Obx(() {
      //           final evalResult = controller.speechEvaluationMap[controller.currentPage.value]?.evalResult;
      //           final score = SpeechEvaluation.instance.getScore(evalResult);
      //           return Visibility(
      //             visible: score != -1,
      //             child: Container(
      //               padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
      //               decoration: BoxDecoration(
      //                 color: score < 60
      //                     ? Get.theme.colorScheme.error
      //                     : (score >= 60 && score < 80 ? Get.theme.colorScheme.onSecondary : Get.theme.primaryColor),
      //                 borderRadius: const BorderRadius.all(Radius.circular(12)),
      //               ),
      //               child: Center(
      //                   child: Text(
      //                 score.toString(),
      //                 style: Get.textTheme.labelMedium?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, letterSpacing: 0),
      //               )),
      //             ),
      //           );
      //         }),
      //       ),
      //     ),
      //   ],
      // ),
      GestureDetector(
        onTap: controller.jumpNextSubtitle,
        child: Container(
          width: iconSize,
          height: iconSize,
          color: Colors.transparent,
        ),
      ),
    ];
  }

  Widget _buildLandscapeRecordingWidget() {
    return Visibility(
      visible: controller.recordingInLsMode.value,
      child: GestureDetector(
        onTap: controller.recordStop,
        child: Container(
          color: Colors.transparent,
          child: IgnorePointer(
            ignoring: true,
            child: WaveformWidget(
              barCount: 30,
              barWidth: 2,
              barSpacing: 4,
              animationDuration: 500,
              delayBetweenBars: 100,
              minHeight: 25,
              maxHeight: 35,
              randomizeDuration: 300,
              random: false,
              barColor: Get.theme.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPortraitRecordingWidget() {
    var iconSize = 54.whs;
    return Visibility(
      visible: controller.recordingInLsMode.value,
      child: GestureDetector(
        onTap: controller.recordStop,
        // child: Container(
        //   color: Colors.transparent,
        //   child: IgnorePointer(
        //     ignoring: true,
        //     child: Column(
        //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //       crossAxisAlignment: CrossAxisAlignment.center,
        //       children: [
        //         WaveformWidget(
        //           barCount: 30,
        //           barWidth: 2,
        //           barSpacing: 4,
        //           animationDuration: 500,
        //           delayBetweenBars: 100,
        //           minHeight: 25,
        //           maxHeight: 35,
        //           randomizeDuration: 300,
        //           random: false,
        //           barColor: Get.theme.primaryColor,
        //         ),
        //         const Gap(4),
        //         Text(
        //           "点击波纹,结束录音",
        //           style: Get.textTheme.bodyLarge,
        //         )
        //       ],
        //     ),
        //   ),
        // ),
        child: GestureDetector(
          onTap: controller.recordStop,
          child: ImageLoader(R.recording, size: iconSize),
        ),
      ),
    );
  }

  Widget _buildPortraitLsModeControl() {
    return SizedBox(
      height: 44,
      width: Get.width,
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.playerMenuItems.length,
        onReorder: (int oldIndex, int newIndex) {
          controller.changeControlMenuSort(newIndex, oldIndex);
        },
        itemBuilder: (context, index) {
          return SizedBox(
            key: ValueKey(controller.playerMenuItems[index].id),
            width: 70,
            child: _getControlMenu(controller.playerMenuItems[index].id),
          );
        },
      ),
    );
  }

  Widget _getControlMenu(int id, {bool isLandscape = false}) {
    Widget buildMenu(String image, {bool useSelect = false, VoidCallback? onTap}) {
      final color = (isLandscape
          ? Colors.white.withValues(alpha: 0.7)
          : Get.isDarkMode
              ? gray400
              : Get.theme.colorScheme.secondary);
      return GestureDetector(
        onTap: onTap,
        child: Container(
          color: Colors.transparent,
          child: Padding(
            padding:
                isLandscape ? EdgeInsets.symmetric(horizontal: 10.whs, vertical: 19.whs) : EdgeInsets.symmetric(horizontal: 19.whs, vertical: 10.whs),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              child: Container(
                key: ValueKey('$image-$useSelect'),
                child: ImageLoader(
                  image,
                  size: 24.whs,
                  color: useSelect ? Get.theme.primaryColor : color,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ),
      );
    }

    switch (id) {
      case PlayerMenuId.note:
        return buildMenu(R.edit, onTap: () => controller.showAddNoteDialog());
      case PlayerMenuId.ai:
        return buildMenu(R.ai, onTap: () => Get.to(AiChatWidget(subtitle: controller.videoKit.subtitles[controller.currentPage.value])));
      case PlayerMenuId.collect:
        return Obx(() => buildMenu(
              controller.videoKit.subtitles.isNotEmpty && controller.sentenceCollectMap[controller.currentPage.value] != null
                  ? (isLandscape ? R.heart_select : R.heart)
                  : (isLandscape ? R.heart : R.heart),
              useSelect:
                  !isLandscape && controller.videoKit.subtitles.isNotEmpty && controller.sentenceCollectMap[controller.currentPage.value] != null,
              onTap: () => controller.switchCollect(),
            ));
      case PlayerMenuId.rate:
        return Obx(
          () => buildMenu(
            controller.videoKit.currentSpeed.value == 1.0 ? R.speed : R.speed_half,
            useSelect: controller.videoKit.currentSpeed.value == 1.0,
            onTap: () => controller.switchSpeed(),
          ),
        );
      case PlayerMenuId.eye:
        return Obx(() => buildMenu(
              controller.showSubtitlePlaceholder.value ? R.eye_close : R.eye_open,
              useSelect: controller.showSubtitlePlaceholder.value,
              onTap: () => controller.toggleSubtitlePlaceholder(),
            ));
      case PlayerMenuId.translate:
        return Obx(() => buildMenu(
              controller.videoKit.currentSubtitleMode.value == SubtitleMode.target.index
                  ? R.translate_target
                  : (controller.videoKit.currentSubtitleMode.value == SubtitleMode.native.index ? R.translate_native : R.translate),
              useSelect: controller.videoKit.currentSubtitleMode.value == SubtitleMode.target.index,
              onTap: () => controller.videoKit.switchSubtitleMode(),
            ));
      case PlayerMenuId.editSubtitle:
        return buildMenu(R.edit_subtitle, onTap: () => controller.goEditSubtitle());
      case PlayerMenuId.skip:
        return Obx(() => buildMenu(controller.videoKit.onlyPlayLines.value ? R.only_lines_active : R.only_lines_off,
            useSelect: controller.videoKit.onlyPlayLines.value, onTap: () => controller.videoKit.switchOnlyPlayLines()));
      case PlayerMenuId.more:
        return buildMenu(R.setting, onTap: () => controller.goMoreSetting());
      default:
        return Obx(() => buildMenu(R.setting));
    }
  }

  Widget _buildLandscapeLsModeControl() {
    return SizedBox(
      width: 70,
      height: Get.height,
      child: ReorderableListView.builder(
        scrollDirection: Axis.vertical,
        itemCount: controller.playerMenuItems.length,
        onReorder: (int oldIndex, int newIndex) {
          controller.changeControlMenuSort(newIndex, oldIndex);
        },
        itemBuilder: (context, index) {
          return Container(
            key: ValueKey(controller.playerMenuItems[index].id),
            child: _getControlMenu(controller.playerMenuItems[index].id, isLandscape: true),
          );
        },
      ),
    );
  }

  Widget _buildOnlyListenSubtitles() {
    return Obx(
      () => Listener(
        onPointerDown: (event) => controller.onPointerDown(),
        onPointerUp: (event) => controller.onPointerUp(),
        child: ListView.builder(
            controller: controller.itemScrollController,
            itemCount: controller.videoKit.subtitles.length,
            itemBuilder: (BuildContext context, int index) {
              return AutoScrollTag(
                key: ValueKey(index),
                controller: controller.itemScrollController,
                index: index,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 20,
                    left: 16,
                    right: 16,
                    bottom: index == controller.videoKit.subtitles.length - 1 ? 16 : 8,
                  ),
                  child: GestureDetector(
                    onTap: () => controller.onSubTitleListClick(index),
                    child: Obx(
                      () => SplitEnglishWidget(
                        subtitle: controller.videoKit.subtitles[index],
                        subtitleMode: controller.videoKit.currentSubtitleMode.value,
                        evalResult: controller.speechEvaluationMap[index]?.evalResult,
                        onPlaceholderTap: () {
                          controller.showSubtitlePlaceholder.value = false;
                        },
                        showPlaceholder: controller.showSubtitlePlaceholder.value,
                      ),
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }
}
