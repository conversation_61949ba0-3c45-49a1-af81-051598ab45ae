import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

//0无效 1收藏 2保存笔记
typedef NoteSaveCallback = void Function(int);

class AddNoteWidget extends StatelessWidget {
  final Subtitle subtitle;
  final int resourceType;
  final String resourceId;
  final String noteId;
  final int videoStartTime;
  final int videoEndTime;
  final NoteSaveCallback noteSaveCallback;
  final String? noteContent;

  const AddNoteWidget({
    super.key,
    required this.subtitle,
    required this.resourceType,
    required this.resourceId,
    required this.noteId,
    required this.noteSaveCallback,
    required this.videoStartTime,
    required this.videoEndTime,
    this.noteContent,
  });

  @override
  Widget build(BuildContext context) {
    return SheetKeyboardDismissible(
      dismissBehavior: const SheetKeyboardDismissBehavior.onDragDown(
        isContentScrollAware: true,
      ),
      child: Sheet(
        decoration: MaterialSheetDecoration(
          size: SheetSize.fit,
          color: Theme.of(context).colorScheme.surface,
          clipBehavior: Clip.antiAlias,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
        ),
        child: _buildReadOnly(context),
      ),
    );
  }

  Widget _buildReadOnly(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.whs, horizontal: 14.whs),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("笔记", style: Get.textTheme.titleLarge),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: const Icon(Icons.close),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 16.whs),
          child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              noteContent ?? '',
              style: TextStyle(fontSize: 16.whs, color: gray700),
            ),
          ),
        ),
        SafeArea(
          top: false,
          child: Padding(
            padding: EdgeInsets.only(left: 16.whs, right: 16.whs, bottom: 16.whs),
            child: Row(
              children: [
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pushReplacement(
                      ModalSheetRoute(
                        swipeDismissible: true,
                        viewportPadding: EdgeInsets.only(
                          top: MediaQuery.viewPaddingOf(Get.context!).top,
                        ),
                        builder: (context) => EditNoteWidget(
                          subtitle: subtitle,
                          resourceType: resourceType,
                          resourceId: resourceId,
                          noteId: noteId,
                          noteSaveCallback: noteSaveCallback,
                          videoStartTime: videoStartTime,
                          videoEndTime: videoEndTime,
                          noteContent: noteContent,
                        ),
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: gray700.withOpacity(0.1),
                      borderRadius: const BorderRadius.all(Radius.circular(8)),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                    child: Text(
                      "编辑",
                      style: TextStyle(color: gray700, fontSize: 16.whs, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
                Gap(16.whs),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class EditNoteWidget extends StatefulWidget {
  final Subtitle subtitle;
  final int resourceType;
  final String resourceId;
  final String noteId;
  final int videoStartTime;
  final int videoEndTime;
  final NoteSaveCallback noteSaveCallback;
  final String? noteContent;

  const EditNoteWidget({
    super.key,
    required this.subtitle,
    required this.resourceType,
    required this.resourceId,
    required this.noteId,
    required this.noteSaveCallback,
    required this.videoStartTime,
    required this.videoEndTime,
    this.noteContent,
  });

  @override
  State<EditNoteWidget> createState() => _EditNoteWidgetState();
}

class _EditNoteWidgetState extends State<EditNoteWidget> {
  final FocusNode _focusNode = FocusNode();
  late TextEditingController textEditingController;

  @override
  void initState() {
    super.initState();
    textEditingController = TextEditingController(text: widget.noteContent ?? "");
    textEditingController.addListener(() {
      setState(() {});
    });
    if (widget.noteId.isNotEmpty && (widget.noteContent == null || widget.noteContent!.isEmpty)) {
      Net.getRestClient().localNote(widget.noteId).then((value) {
        textEditingController.text = value.data.content ?? "";
      });
    }
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  void saveNote() {
    if (textEditingController.text.isEmpty) {
      "请输入笔记内容".toast;
      return;
    }
    Net.getRestClient().addNote({
      'id': widget.noteId,
      'resourceId': widget.resourceId,
      'resourceType': widget.resourceType,
      'content': textEditingController.text,
      'videoStartTime': widget.videoStartTime,
      'videoEndTime': widget.videoEndTime,
    }).then((value) {
      widget.noteSaveCallback(2);
      Navigator.of(context).pop(); // 关闭编辑页，回到只读页
    });
  }

  @override
  Widget build(BuildContext context) {
    return SheetKeyboardDismissible(
      dismissBehavior: const SheetKeyboardDismissBehavior.onDragDown(
        isContentScrollAware: true,
      ),
      child: Sheet(
        decoration: MaterialSheetDecoration(
          size: SheetSize.stretch,
          color: Theme.of(context).colorScheme.surface,
          clipBehavior: Clip.antiAlias,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
        ),
        child: SheetContentScaffold(
          bottomBarVisibility: const BottomBarVisibility.always(
            ignoreBottomInset: true,
          ),
          body: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(vertical: 12.whs, horizontal: 14.whs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("笔记", style: Get.textTheme.titleLarge),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.whs),
                  child: TextField(
                    focusNode: _focusNode,
                    decoration: const InputDecoration(
                      hintText: "输入笔记内容...",
                      contentPadding: EdgeInsets.zero,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                    ),
                    controller: textEditingController,
                    cursorColor: Get.theme.primaryColor,
                    keyboardType: TextInputType.multiline,
                    maxLines: null,
                    textInputAction: TextInputAction.newline,
                    style: TextStyle(
                      fontSize: 16.whs,
                    ),
                  ),
                ),
              ),
            ],
          ),
          bottomBar: SafeArea(
            top: false,
            child: Padding(
              padding: EdgeInsets.only(left: 16.whs, right: 16.whs, bottom: 16.whs),
              child: Row(
                children: [
                  const Spacer(),
                  Container(
                    decoration: BoxDecoration(
                      color: gray700.withOpacity(0.1),
                      borderRadius: const BorderRadius.all(Radius.circular(8)),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                    child: Text(
                      "收藏",
                      style: TextStyle(color: gray700, fontSize: 16.whs, fontWeight: FontWeight.w500),
                    ),
                  ),
                  Gap(10.whs),
                  GestureDetector(
                    onTap: () => saveNote(),
                    child: Container(
                      decoration: BoxDecoration(
                        color: gray700.withOpacity(0.1),
                        borderRadius: const BorderRadius.all(Radius.circular(8)),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                      child: Text(
                        "保存",
                        style:
                            TextStyle(color: textEditingController.text.isEmpty ? gray400 : gray700, fontSize: 16.whs, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                  Gap(16.whs),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
