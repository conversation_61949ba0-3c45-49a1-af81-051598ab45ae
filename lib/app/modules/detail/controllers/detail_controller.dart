import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:back_button_interceptor/back_button_interceptor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as fcm;
import 'package:flutter_sound/flutter_sound.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/base_controller.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';
import 'package:lsenglish/model/speech_evaluation_resp.dart';
import 'package:lsenglish/model/video_time_interval.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/datacenter_time_manager_extension.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:lsenglish/utils/player_menu.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/url.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:lsenglish/utils/video_compression.dart';
import 'package:lsenglish/utils/xfyun.dart';
import 'package:lsenglish/video/media_player.dart';
import 'package:lsenglish/widgets/base_dialog.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:path/path.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

import '../../../../model/local_detail_resp/resource_detail_resp.dart';
import '../../../../video/video_controls_material.dart';
import '../../../../widgets/lstimes_mod_widget.dart';
import '../views/add_note_widget.dart';
import '../views/more.dart';
import 'detail_util.dart';

class DetailController extends BaseController with GetTickerProviderStateMixin, WidgetsBindingObserver {
  final SpeechEvaluation _recognitionService = SpeechEvaluation.instance;
  var isLocalVideo = false;
  var remoteResourceId = "";
  FlutterSoundPlayer recordSoundPlayer = FlutterSoundPlayer(logLevel: Level.fatal);
  //进入页面的时候优先判断，免得每次在录制的判断，影响效率
  var hasMicPermission = false;
  var videoKit = MediaPlayer();
  var isLandscape = false.obs;
  //是否为用户手动跳转
  var pageChangeByUser = false.obs;
  late var videoController = VideoController(videoKit.getPlayer);
  final AutoScrollController itemScrollController = AutoScrollController(axis: Axis.vertical);
  late PageController pageController;
  bool _allowAutoScroll = true;
  Timer? _autoScrollTimer;
  var recordingInLsMode = false.obs;
  var _videoUrlOrPath = "";
  var videoName = "".obs;
  ResourceDetailResp? localDetailResp;
  var showSubtitlePlaceholder = true.obs;
  var notesMap = <int, NoteModel>{}.obs;
  var sentenceCollectMap = <int, VideoTimeInterval?>{}.obs;
  var currentPage = 0.obs;
  var loadingSubtitle = true.obs;
  var currentToast = "".obs;
  var showCurrentLandScapeToast = false.obs;
  late var videoControlsCallbacks = VideoControlsCallbacks(
    onToggleFullscreen: () => videoKit.toggleFullscreen(),
    onDoubleTapPlayOrPause: () => videoKit.resetLsModeIndex(needPlay: false),
    onPlayOrPause: () => videoKit.resetLsModeIndex(),
    onPointerUp: () => videoKit.resetLsModeIndex(needPlay: false),
    onVideoSeekChangeEnd: () => videoKit.onVideoSeekChangeEnd(),
    onTap: () => videoKit.toggleFullscreen(),
  );
  var playerMenuItems = <PlayerMenu>[].obs;
  var totalDuration = 0.obs;
  var seekBarRatio = 0.0.obs;
  var canChangeSeekBarRatio = true;
  var isAppBarVisible = true.obs;
  var speechEvaluationMap = <int, SpeechEvaluationResp>{}.obs;

  @override
  void onInit() async {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    BackButtonInterceptor.add(_interceptorPop);
    itemScrollController.addListener(_scrollListener);
    playerMenuItems.value = PlayerMenuManager().menuByConfig();
    pageController = PageController(keepPage: false);
    videoKit.init();
    videoKit.reset();
    remoteResourceId = Get.arguments['resourceId'] ?? "";
    _videoUrlOrPath = Get.arguments['videoUrlOrPath'] ?? "";
    videoName.value = Get.arguments['videoName'] ?? "";
    isLocalVideo = remoteResourceId.isEmpty;
    logger(
        "DetailController init remoteResourceId=$remoteResourceId _videoUrlOrPath=$_videoUrlOrPath videoName=$videoName isLocalVideo=$isLocalVideo");
    if (isLocalVideo) {
      await checkVideo(_videoUrlOrPath);
    }
    _initListener();
    fetchDetailData();
    hasMicPermission = await Permission.microphone.isGranted;
  }

  @override
  void onReady() {
    super.onReady();
    videoKit.exitNativeFullscreen();
  }

  void _initListener() async {
    subscriptions.clear();
    subscriptions.addAll([
      videoKit.currentSubtitleIndex.listen((index) async {
        logger("currentSubtitleIndex listen currentSubtitleIndex= $index _pageChangeByUser=${pageChangeByUser.value}");
        currentPage.value = index;
        if (videoKit.openLsMode.value) {
          if (!pageChangeByUser.value) {
            //如果是用户自己滑动 不需要做任何处理
            // 当更改了进度之后 也需要stopPlayer
            //1. 当用户主动滑动的时候，如果出现了跳过的逻辑，是需要触发这里的jump的
            recordSoundPlayer.stopPlayer();
            _recognitionService.stop();
            jumpToPage(index);
            recordingInLsMode.value = false;
          }
        } else {
          subtitleListScrollToCenter();
        }
      }),
      videoKit.seekSkipSubtitleIndex.listen((index) {
        jumpToPage(index);
      }),
      videoKit.playing.listen((playing) {
        int index = getCurrentSubtitleIndex();
        bool isLSMode = videoKit.openLsMode.value;
        if (playing) {
          DataCenterHelper.resumeSession();
          DataCenterHelper.recordPlayEvent(
            isStart: true,
            index: index,
            isLSMode: isLSMode,
            subtitles: videoKit.subtitles,
          );
        } else {
          DataCenterHelper.recordPlayEvent(
            isStart: false,
            index: index,
            isLSMode: isLSMode,
            subtitles: videoKit.subtitles,
          );
        }
      }),
      videoKit.getPlayer.stream.position.listen((duration) {
        if (canChangeSeekBarRatio) {
          seekBarRatio.value = totalDuration.value == 0 ? 0 : duration.inMilliseconds / totalDuration.value;
          // debugPrint("duration.inMilliseconds =${duration.inMilliseconds} totalDuration=$totalDuration seekBarRatio=${seekBarRatio.value}");
        }
      }),
      videoKit.getPlayer.stream.duration.listen((event) {
        totalDuration.value = event.inMilliseconds;
      }),
      videoKit.pausedInLsMode.listen((paused) {
        logger("handleLsMode paused=$paused");
        if (paused && Config().playerConfig.autoRecord) {
          if (DateTime.now().millisecondsSinceEpoch - videoKit.onHorizontalDragEndTime < 1000) {
            logger("handleLsMode close autoRecord cause time offset < 1000");
            videoKit.onHorizontalDragEndTime = 0;
            return;
          }
          recordStart();
        }
      }),
      videoKit.seekByHistoryIndex.listen((index) {
        //这里为了等待pageview先跳转到指定的位置 保证不会跳到第一句然后又跳到对应的进度所对应的index
        jumpToPage(index);
        loadingSubtitle.value = false;
      }),
      ObsUtil().loginStatus.listen((isLogin) {
        if (isLogin) {
          fetchDetailData();
        }
      }),
      Xfyun().completed.listen((data) {
        logger("Xfyun completed = $data");
        if (Xfyun().isCompleted(localDetailResp?.resourceId)) {
          loadingSubtitle.value = true;
          fetchDetailData();
        }
      }),
      ObsUtil().uploadSubtitle.listen((v) async {
        logger("receive uploadSubtitle obs");
        loadingSubtitle.value = true;
        localDetailResp = v;
        await loadSubtitle();
        loadingSubtitle.value = false;
      }),
    ]);
    initVoiceListen();
  }

  Future<void> checkVideo(String videoPath) async {
    var videoInfo = await FFmpegUtils().getVideoInfo(videoPath);
    logger("videoInfo width=${videoInfo['width']} height=${videoInfo['height']} frameRate=${videoInfo['frameRate']}");
    if (FFmpegUtils().isVideoOver4K(videoInfo['width'] ?? 0, videoInfo['height'] ?? 0)) {
      videoKit.pause();
      Get.dialog(
        CommonDialog(title: "视频分辨率大于等于4K,会影响体验\n是否进行视频压缩？", options: const [
          "确定"
        ], callbacks: [
          () => {
                VideoCompressionUtil().addCompress(VideoCompression(videoPath: videoPath)),
                Get.back(),
              }
        ]),
        barrierDismissible: false,
      );
    }
    await videoKit.open(_videoUrlOrPath);
  }

  Future initVoiceListen() async {
    await recordSoundPlayer.openPlayer();
    // 初始化语音识别服务
    await _recognitionService.init();
    _recognitionService.resultNotifier.addListener(() async {
      final result = _recognitionService.resultNotifier.value;
      if (result != null) {
        await _handleSpeechEvaluationResult(result);
      }
    });
    // 监听评测警告并toast第一个warning
    SpeechEvaluation.instance.warningNotifier.addListener(() {
      final warnings = SpeechEvaluation.instance.warningNotifier.value;
      final msg = SpeechEvaluation.getWarningTextForList(warnings);
      if (msg != null && msg.isNotEmpty) {
        msg.toast;
      }
    });
  }

  /// 处理语音评测结果
  Future<void> _handleSpeechEvaluationResult(SpeechEvaluationResult result) async {
    logger("收到语音评测结果: eof=${result.eof}");

    // 如果是最终结果，立即更新本地UI，然后上传到服务器
    if (result.eof == 1) {
      final currentIndex = getCurrentSubtitleIndex();
      final subtitle = videoKit.subtitles[currentIndex];

      // 获取最新的录音路径
      String? localAudioPath;
      try {
        localAudioPath = await _recognitionService.stkouyuPlugin.getLastRecordPath();
        logger("获取到本地录音路径: $localAudioPath");
      } catch (e) {
        logger("获取本地录音路径失败: $e");
      }

      // 立即创建临时的评测记录更新UI
      final tempEvaluation = SpeechEvaluationResp(
        id: null, // 临时记录没有ID
        resourceId: localDetailResp?.resourceId,
        resourceType: localDetailResp?.resourceType,
        content: subtitle.targetData,
        audioUrl: result.audioUrl,
        startTime: subtitle.start.inMilliseconds,
        endTime: subtitle.end.inMilliseconds,
        evalResult: result,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
        localAudioPath: localAudioPath, // 设置本地音频路径
      );

      // 立即更新本地UI
      speechEvaluationMap[currentIndex] = tempEvaluation;

      // 异步上传到服务器
      _uploadSpeechEvaluationResult(result, currentIndex, tempEvaluation);
    }
  }

  /// 上传语音评测结果到服务器
  Future<void> _uploadSpeechEvaluationResult(SpeechEvaluationResult result, int subtitleIndex, SpeechEvaluationResp tempEvaluation) async {
    if (localDetailResp == null) {
      logger("localDetailResp为空，无法上传语音评测结果");
      return;
    }

    try {
      final subtitle = videoKit.subtitles[subtitleIndex];
      final existingEvaluation = speechEvaluationMap[subtitleIndex];

      // 判断是新增还是更新
      final bool isUpdate = existingEvaluation?.id != null;

      if (isUpdate) {
        // 更新现有记录
        final response = await Net.getRestClient().updateSpeechEvaluation({
          'id': existingEvaluation!.id!,
          'resourceId': localDetailResp!.resourceId!,
          'resourceType': localDetailResp!.resourceType!,
          'content': jsonEncode(result),
          'audioUrl': result.audioUrl ?? '',
          'startTime': subtitle.start.inMilliseconds,
          'endTime': subtitle.end.inMilliseconds,
        });

        // 更新本地数据（替换临时数据）
        speechEvaluationMap[subtitleIndex] = response.data;
        speechEvaluationMap[subtitleIndex]?.evalResult = result;
        speechEvaluationMap[subtitleIndex]?.localAudioPath = existingEvaluation.localAudioPath;

        logger("语音评测结果更新成功: ${response.data.id}");
      } else {
        // 创建新记录
        final response = await Net.getRestClient().createSpeechEvaluation({
          'resourceId': localDetailResp!.resourceId!,
          'resourceType': localDetailResp!.resourceType!,
          'content': jsonEncode(result),
          'audioUrl': result.audioUrl ?? '',
          'startTime': subtitle.start.inMilliseconds,
          'endTime': subtitle.end.inMilliseconds,
        });

        // 用服务器返回的数据替换临时数据
        speechEvaluationMap[subtitleIndex] = response.data;
        speechEvaluationMap[subtitleIndex]?.evalResult = SpeechEvaluationResult.fromJson(jsonDecode(response.data.content!));
        speechEvaluationMap[subtitleIndex]?.localAudioPath = existingEvaluation?.localAudioPath;
        debugPrint("speechEvaluationMap subtitleIndex =$subtitleIndex localAudioPath = ${speechEvaluationMap[subtitleIndex]?.localAudioPath}");

        logger("语音评测结果创建成功: ${response.data.id}");
      }
    } catch (e) {
      logger("上传语音评测结果失败: $e");
      // 上传失败时，保留临时数据，用户仍能看到评测结果
    }
  }

  /// 获取语音评测列表
  Future<void> _fetchSpeechEvaluationList() async {
    if (localDetailResp == null) {
      return;
    }

    try {
      final response = await Net.getRestClient().getSpeechEvaluations(
        localDetailResp!.resourceId!,
        localDetailResp!.resourceType!,
      );

      // 构建索引映射
      speechEvaluationMap.clear();
      for (final evaluation in response.data) {
        // 根据时间范围匹配字幕索引
        final index = _findSubtitleIndexByTime(evaluation.startTime, evaluation.endTime);
        if (index != -1) {
          speechEvaluationMap[index] = evaluation;
          // 新增：content转evalResult
          if (evaluation.content != null && evaluation.content!.isNotEmpty) {
            try {
              evaluation.evalResult = SpeechEvaluationResult.fromJson(jsonDecode(evaluation.content!));
            } catch (e) {
              logger("content转evalResult失败: $e");
            }
          }
        }
      }

      logger("获取语音评测列表成功，共${speechEvaluationMap.length}条记录");
    } catch (e) {
      logger("获取语音评测列表失败: $e");
    }
  }

  /// 根据时间范围查找字幕索引
  int _findSubtitleIndexByTime(int? startTime, int? endTime) {
    if (startTime == null || endTime == null) return -1;

    for (int i = 0; i < videoKit.subtitles.length; i++) {
      final subtitle = videoKit.subtitles[i];
      if (subtitle.start.inMilliseconds == startTime && subtitle.end.inMilliseconds == endTime) {
        return i;
      }
    }
    return -1;
  }

  void closeLsMode() {
    videoKit.openLsMode.value = false;
    subtitleListScrollToCenter();
  }

  void openLsMode() {
    videoKit.openLsMode.value = true;
    jumpToPage(videoKit.currentSubtitleIndex.value);
  }

  void jumpToPage(int index) {
    if (isLandscape.value) {
      return;
    }
    if (index >= 0 && index < videoKit.subtitles.length) {
      if (!pageController.hasClients) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          pageController.jumpToPage(index);
        });
      } else {
        pageController.jumpToPage(index);
      }
    }
  }

  void jumpPreSubtitle() {
    videoKit.skipFindReverse = true;
    videoKit.preSubtitle();
    videoKit.play();
  }

  void jumpNextSubtitle() {
    videoKit.skipFindReverse = false;
    videoKit.nextSubtitle();
    videoKit.play();
  }

  void backWhenFullscreen() {
    videoKit.exitNativeFullscreen();
  }

  void toggleSubtitlePlaceholder() {
    showSubtitlePlaceholder.value = !showSubtitlePlaceholder.value;
  }

  void recordStart() async {
    if (!hasMicPermission) {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.microphone,
      ].request();
      // 检查每个权限的状态
      if (statuses[Permission.microphone]?.isGranted == false) {
        requestVoicePermission(title: "需要录音权限");
        return;
      }
    }
    int index = getCurrentSubtitleIndex();
    bool isLSMode = videoKit.openLsMode.value;
    DataCenterHelper.recordRecordEvent(
      isStart: true,
      index: index,
      isLSMode: isLSMode,
      subtitles: videoKit.subtitles,
    );
    _recordStart();
  }

  void requestVoicePermission({String title = "需要录音和语音识别权限"}) {
    if (Platform.isIOS || Platform.isAndroid) {
      CommonDialog(title: title, options: const [
        "跳转"
      ], callbacks: [
        () => {
              openAppSettings(),
            },
      ]).showDialog;
    }
  }

  void _recordStart() async {
    logger("_recordStart playingInLsMode to false");
    await videoKit.pause();
    if (videoKit.playing.value || recordSoundPlayer.isPlaying) {
      await recordSoundPlayer.stopPlayer();
    }

    videoKit.playing.value = false;
    var recordFilePath = await FileUtils().getStringByDir(FileUtils().getRecordDir());
    logger("recordStart _recordFilePath = $recordFilePath");

    recordingInLsMode.value = true;
    _recognitionService.startSentence(videoKit.subtitles[currentPage.value].targetData,
        localAudioFilePath: recordFilePath,
        localAudioFileName: "${basenameWithoutExtension(filterVideoUrl(_videoUrlOrPath))}${videoKit.currentSubtitleIndex.value}.mp3");
  }

  void recordStop({bool forceThrowRecord = false}) async {
    logger("recordStop start");
    videoKit.pause();
    recordingInLsMode.value = false;
    await _recognitionService.stop();
    int index = getCurrentSubtitleIndex();
    bool isLSMode = videoKit.openLsMode.value;
    DataCenterHelper.recordRecordEvent(
      isStart: false,
      index: index,
      isLSMode: isLSMode,
      subtitles: videoKit.subtitles,
    );
    if (forceThrowRecord) {
      logger("recordStop forceThrowRecord return");
      return;
    }
    playRecordWhenRecordEnd(await _recognitionService.getLastRecordPath() ?? "");
    if (Config().playerConfig.showSubtitleWhenRecordEnd) {
      showSubtitlePlaceholder.value = false;
    }
  }

  void playRecordWhenRecordEnd(String path) async {
    logger("playRecordWhenRecordEnd path=$path");
    if (path.isEmpty) {
      return;
    }
    if (Config().playerConfig.autoPlayRecordWhenRecordEnd) {
      try {
        //可能是调用了recordSoundPlayer的stop之后 还是有一些时间需要回收
        //不加就会导致偶尔出现录制结束后播放录音只播放了一点点
        await Future.delayed(const Duration(milliseconds: 350));
        await videoKit.pause();
        recordSoundPlayer.startPlayer(fromURI: path);
      } catch (e) {
        logger("recordStop 录音播放失败: $e");
      }
    }
  }

  //播放录音的时候需要暂停视频
  void playRecord() async {
    var index = getCurrentSubtitleIndex();
    try {
      var evaluation = speechEvaluationMap[index];
      var audioUrl = evaluation?.audioUrl;
      var localAudioPath = evaluation?.localAudioPath;
      logger("index=$index audioUrl =$audioUrl, localAudioPath =$localAudioPath");

      // 优先使用本地音频路径
      String? playPath;
      if (localAudioPath != null && localAudioPath.isNotEmpty) {
        playPath = localAudioPath;
        logger("使用本地音频路径播放: $playPath");
      } else if (audioUrl != null && audioUrl.isNotEmpty) {
        playPath = ensureHttps(audioUrl);
        logger("使用网络音频路径播放: $playPath");
      }

      if (playPath != null) {
        recordSoundPlayer.startPlayer(
          fromURI: playPath,
          whenFinished: () {
            recordSoundPlayer.stopPlayer();
            videoKit.unmute();
          },
        );
      } else {
        "你还没有录制的声音".toast;
      }
    } catch (e) {
      logger("录音播放失败: $e");
    }
  }

  int getCurrentSubtitleIndex() {
    if (isLandscape.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (!videoKit.openLsMode.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (pageController.hasClients) {
      return pageController.page?.toInt() ?? 0;
    }
    return 0;
  }

  //如果正在播放 那就暂停
  //如果已经暂停就继续播放 如果暂停的时候停留的时间和当前字幕的结尾 那么从头开始播放
  void lsPlayClick() async {
    logger("lsPlayClick");
    if (!isLandscape.value && pageController.hasClients && pageController.page == null && videoKit.openLsMode.value) {
      logger("lsPlayClick pageController.page null");
      return;
    }
    await _recognitionService.stop();
    playCurrentLsIndex();
  }

  Future<void> playCurrentLsIndex() async {
    var index = getCurrentSubtitleIndex();
    logger("playCurrentLsIndex playingInLsMode = ${videoKit.playing.value} index=$index");
    if (videoKit.isPlaying()) {
      logger("playCurrentLsIndex pause when playingInLsMode to false");
      videoKit.pause();
    } else {
      if (videoKit.currentDurationIsMatchCurrentSubtitleEndInLsMode(index: index)) {
        logger("playCurrentLsIndex seekBySubtitleIndex index=$index");
        // recordText.value = "";
        await videoKit.seekBySubtitleIndex(index);
        await videoKit.play();
      } else {
        logger("playCurrentLsIndex play");
        videoKit.play();
      }
    }
  }

  void lsContainerClick(int index) async {
    recordStop(forceThrowRecord: true);
    // recordText.value = "";
    await videoKit.seekBySubtitleIndex(index);
    await videoKit.play();
  }

  void switchSpeed() async {
    await videoKit.switchSpeed();

    lsContainerClick(currentPage.value);
    showCurrentLandScapeToast.value = true;
    currentToast.value = "已切换至${videoKit.currentSpeed.value}倍数";
    await Future.delayed(Duration(seconds: 1));
    showCurrentLandScapeToast.value = false;
  }

  void onUserScrollStart() {
    pageChangeByUser.value = true;
  }

  void onPageScrollDirection(bool isForward) {
    videoKit.skipFindReverse = !isForward;
  }

  void onUserScrollEnd() {
    pageChangeByUser.value = false;
  }

  void onUserScroll(UserScrollNotification userScroll) {
    videoKit.skipFindReverse = userScroll.direction == ScrollDirection.forward;
  }

  //改变index的时候 需要暂停视频、关闭录音 关闭录音播放
  //当更改了进度之后 也需要stopPlayer
  void onPageChanged(int index) async {
    currentPage.value = index;
    logger("onPageChanged index = $index  _pageChangeByUser= ${pageChangeByUser.value}");

    if (!pageChangeByUser.value) {
      return;
    }
    if (recordingInLsMode.value) {
      _recognitionService.stop();
    }
    videoKit.unmute();
    recordSoundPlayer.stopPlayer();

    logger("onPageChanged pause");
    videoKit.pause(); //为了解决显示loading的问题 先pause 再play
    recordingInLsMode.value = false;
    onSubTitleListClick(index);
  }

  void onSubTitleListClick(int index) {
    // recordText.value = "";
    videoKit.seekBySubtitleIndex(index);
    // 在LS模式下，确保重置暂停状态
    if (videoKit.openLsMode.value) {
      videoKit.pausedInLsMode.value = false;
    }
    videoKit.play();
  }

  void goMoreSetting() {
    videoKit.pause();
    _recognitionService.stop();
    recordSoundPlayer.stopPlayer();
    recordingInLsMode.value = false;
    Get.bottomSheet(
      const PlayerMoreWidget(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 2,
      barrierColor: Colors.black.withOpacity(0.5),
      enableDrag: true,
    );
  }

  void goEditSubtitle() async {
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }

    Get.toNamed(Routes.SUBTITLE_EDIT, arguments: {
      'subtitleUrl': localDetailResp!.subtitleUrl,
      'resourceId': localDetailResp!.resourceId,
      'skipList': localDetailResp!.skipList,
      'resourceType': localDetailResp!.resourceType,
      'index': getCurrentSubtitleIndex(),
      'nativeLangSubtitleUrl': localDetailResp?.nativeLangSubtitleUrl,
      'originSubtitleUrl': localDetailResp?.originSubtitleUrl,
    });
  }

  void goSubtitleSearch() async {
    var subtitleAddResult = await Get.toNamed(Routes.SUBTITLE_ADD, arguments: {'videoPath': _videoUrlOrPath});
    if (subtitleAddResult != null) {
      var subtitlePath = subtitleAddResult['subtitlePath'];
      logger("DetailController goSubtitleSearch subtitlePath=$subtitlePath");
      whenSubtitleAdd(subtitlePath);
    }
  }

  void goAIGenSubtitle() async {
    Get.toNamed(Routes.AUDIO_CONVERT, arguments: {
      'videoPath': _videoUrlOrPath,
      'localResourceId': localDetailResp?.resourceId,
    });
  }

  void getSubtitleByVideo() async {
    var subtitlePath = await videoKit.getSubtitleByVideo(_videoUrlOrPath);
    if (subtitlePath.isEmpty) {
      "暂时无法解析出目录".toast;
      return;
    }
    whenSubtitleAdd(subtitlePath);
  }

  Future<void> whenSubtitleAdd(String subtitleUrl) async {
    SPUtil().saveHistory(filterVideoUrl(_videoUrlOrPath), subtitleLocalPath: subtitleUrl);
    videoKit.resetLsModeIndex(needPlay: false);
    await videoKit.loadSubtitles(subtitleUrl);
    logger("loadSubtitle uploadSubtitle $subtitleUrl  whenSubtitleAdd");
    await SubtitleUtil().uploadSubtitle(
      subtitleUrl,
      localDetailResp?.resourceId,
      localDetailResp?.resourceType,
      autoAddSkipWhenSubtitleTargetNone: true,
      needRemoveSubtitleUrl: localDetailResp?.subtitleUrl,
    );
  }

  void subtitleListScrollToCenter() {
    if (isLandscape.value) {
      return;
    }
    if (!_allowAutoScroll) {
      return;
    }
    itemScrollController.scrollToIndex(currentPage.value, preferPosition: AutoScrollPosition.middle);
  }

  void _scrollListener() {
    if (itemScrollController.position.userScrollDirection == ScrollDirection.reverse) {
      // 向上滑动，隐藏 AppBar
      isAppBarVisible.value = false;
    } else if (itemScrollController.position.userScrollDirection == ScrollDirection.forward) {
      isAppBarVisible.value = true;
    }
  }

  void onPointerDown() {
    _autoScrollTimer?.cancel();
    _allowAutoScroll = false;
  }

  void onPointerUp() {
    _autoScrollTimer = Timer(const Duration(seconds: 3), () {
      _allowAutoScroll = true;
    });
  }

  @override
  void didChangeMetrics() {
    // _recognitionService.sessionLost();
    // ignore: deprecated_member_use
    final metrics = WidgetsBinding.instance.window.physicalSize;
    final width = metrics.width;
    final height = metrics.height;
    isLandscape.value = width > height;
    if (!isLandscape.value) {
      jumpToPage(videoKit.currentSubtitleIndex.value);
    } else {
      lsContainerClick(videoKit.currentSubtitleIndex.value);
    }
  }

  void onVisibilityLost() {
    DataCenterHelper.pauseSession();
    videoKit.pause();
    _recognitionService.stop();
    recordSoundPlayer.stopPlayer();
    recordingInLsMode.value = false;
  }

  void onVisibilityGained() {
    DataCenterHelper.resumeSession();
  }

  void goNoteList() async {
    Get.toNamed(Routes.NOTELIST, arguments: {
      'subtitlePath': videoKit.subtitlePath,
    });
  }

  void showAddNoteDialog() {
    int index = currentPage.value;
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }
    final note = localDetailResp!.notes?.firstWhereOrNull((element) =>
      element.videoStartTime == videoKit.subtitles[index].start.inMilliseconds &&
      element.videoEndTime == videoKit.subtitles[index].end.inMilliseconds
    );
    Navigator.push(
      Get.context!,
      ModalSheetRoute(
        swipeDismissible: true,
        viewportPadding: EdgeInsets.only(
          top: MediaQuery.viewPaddingOf(Get.context!).top,
        ),
        builder: (context) => AddNoteWidget(
          subtitle: videoKit.subtitles[index],
          noteId: note?.id ?? '',
          noteContent: note?.content ?? '',
          noteSaveCallback: (type) {
            if (type == 2) {
              fetchDetailData();
            }
          },
          resourceType: localDetailResp!.resourceType!,
          resourceId: localDetailResp!.resourceId!,
          videoStartTime: videoKit.subtitles[index].start.inMilliseconds,
          videoEndTime: videoKit.subtitles[index].end.inMilliseconds,
        ),
      ),
    );
  }

  Future<void> fetchDetailData() async {
    if (!isLogin()) {
      return loadSubtitle();
    }
    await Net.getRestClient().getVideoDetail({
      'localVideoPath': isLocalVideo ? _videoUrlOrPath : "",
      'fileName': isLocalVideo ? basenameWithoutExtension(filterVideoUrl(_videoUrlOrPath)) : "",
      'resourceId': remoteResourceId,
      'resourceType': isLocalVideo ? 2 : 1,
    }).then((v) async {
      logger("getVideoDetail success resp=${jsonEncode(v.data)}");
      localDetailResp = v.data;

      if (isLocalVideo) {
        // 检查本地文件是否存在
        final videoFile = File(_videoUrlOrPath);
        bool localFileExists = await videoFile.exists();

        if (!localFileExists && localDetailResp?.playUrl != null && localDetailResp!.playUrl!.isNotEmpty) {
          logger("本地文件不存在，使用服务器远程地址: ${localDetailResp!.playUrl}");
          _videoUrlOrPath = localDetailResp!.playUrl!;
          await videoKit.open(_videoUrlOrPath);
          downloadVideoCache(_videoUrlOrPath);
        } else if (!localFileExists) {
          logger("本地文件不存在且服务器无远程地址");
          "视频文件不存在".toast;
          return;
        } else if (localDetailResp?.playUrl == null || localDetailResp!.playUrl!.isEmpty) {
          // 本地文件存在但服务器无远程地址，提示上传
          videoKit.pause();
          await Get.dialog(
            CommonDialog(title: "检测到本地视频资源，是否上传到服务端以便同步学习进度？", options: const [
              "上传",
              "暂不上传"
            ], callbacks: [
              () async {
                logger("用户选择上传本地视频到服务端");
                "正在后台上传视频文件，成功后会通知您".toast;
                _uploadVideoInBackground(_videoUrlOrPath);
              },
              () {
                logger("用户选择暂不上传本地视频");
              }
            ]),
            barrierDismissible: false,
          );
          videoKit.play();
        }
      } else {
        _videoUrlOrPath = localDetailResp!.playUrl ?? "";
        var cacheVideoFile = await fcm.DefaultCacheManager().getFileFromCache(filterVideoUrl(_videoUrlOrPath));

        if (cacheVideoFile == null) {
          logger("getVideoDetail cacheVideoFile is null start download");
          await videoKit.open(_videoUrlOrPath);
          downloadVideoCache(_videoUrlOrPath);
        } else {
          _videoUrlOrPath = cacheVideoFile.file.path;
          logger("getVideoDetail cacheVideoFile is not null,use cache path=$_videoUrlOrPath");
          await videoKit.open(_videoUrlOrPath);
        }
      }
      DataCenterHelper.beginSession(
        resourceId: localDetailResp?.resourceId,
        resourceType: localDetailResp?.resourceType,
        lsTimes: localDetailResp?.currentLsTimes,
      );
      if (videoKit.positionInit == -1) {
        videoKit.positionInit = localDetailResp!.position ?? 0;
      }
      await loadSubtitle();
      // 获取语音评测列表
      await _fetchSpeechEvaluationList();
    }).catchError((err) async {
      loadSubtitleWhenError(err.toString());
    });
  }

  //处理和字幕相关的逻辑
  void relationSubtitle2IndexMap() {
    logger("processRelationSubtitle");
    notesMap.clear();
    if (localDetailResp!.notes != null) {
      processNotesIntervals2Map(localDetailResp!.notes!, notesMap, videoKit.subtitles);
    }

    if (localDetailResp!.sentenceCollects != null) {
      processIntervals2Map(localDetailResp!.sentenceCollects!, sentenceCollectMap, videoKit.subtitles);
    }
    videoKit.skipList = getIndexsFromIntervalList(localDetailResp?.skipList ?? [], videoKit.subtitles);
    logger("processRelationSubtitle videoKit.skipList=${videoKit.skipList}");
  }

  Future<void> loadSubtitleWhenError(String err) async {
    loadingSubtitle.value = false;
    logger("loadSubtitle error=$err");
    videoKit.play();
  }

  Future<void> loadSubtitle() async {
    logger("loadSubtitle start");
    var subtitlePath = "";
    subtitlePath = localDetailResp!.subtitleUrl!;
    logger("loadSubtitle subtitlePath from remote $subtitlePath");
    if (isLocalVideo && subtitlePath.isEmpty && localDetailResp!.nativeLangSubtitleUrl != "") {
      //说明没有从服务端获取到字幕文件 默认使用历史保存的
      var localHistoryModel = await SPUtil().getHistory(_videoUrlOrPath);
      logger("loadSubtitle localHistoryModel=${jsonEncode(localHistoryModel)}");
      if (localHistoryModel == null || localHistoryModel.subtitleLocalPath == null || localHistoryModel.subtitleLocalPath?.isEmpty == true) {
        SPUtil().saveHistory(_videoUrlOrPath);
        logger("loadSubtitle localHistoryModel is null");
        subtitlePath = await videoKit.getSubtitleByVideo(_videoUrlOrPath);
      } else {
        subtitlePath = localHistoryModel.subtitleLocalPath ?? "";
      }
    }
    logger("loadSubtitle subtitlePath final load $subtitlePath");
    if (shouldUseDoubleSubtitle(localDetailResp)) {
      logger("loadSubtitle remoteresource load target ${localDetailResp!.originSubtitleUrl} and native ${localDetailResp!.nativeLangSubtitleUrl} ");
      await videoKit.loadSubtitles(localDetailResp!.originSubtitleUrl ?? "", nativeSubtitlePath: localDetailResp!.nativeLangSubtitleUrl ?? "");
    } else {
      await videoKit.loadSubtitles(subtitlePath);
    }
    logger("loadSubtitle finish");
    relationSubtitle2IndexMap();
    // videoKit.delaySubtitle(localHistoryModel.subtitleDelayInMilliseconds);
  }

  void switchCollect() {
    int index = currentPage.value;
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }
    var isRemove = sentenceCollectMap[index] != null;
    var videoTimeInterval =
        VideoTimeInterval(start: videoKit.subtitles[index].start.inMilliseconds, end: videoKit.subtitles[index].end.inMilliseconds);

    if (isRemove) {
      sentenceCollectMap[index] = null;
      sentenceCollectMap.refresh();
      Net.getRestClient().removeLocalSencenceCollect({
        'resourceId': localDetailResp!.resourceId,
        'resourceType': localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
    } else {
      sentenceCollectMap[index] = videoTimeInterval;
      sentenceCollectMap.refresh();
      Net.getRestClient().addLocalSencenceCollect({
        'resourceId': localDetailResp!.resourceId,
        'resourceType': localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
    }
  }

  void updateVideoPosition(int position) async {
    if (localDetailResp == null) {
      return;
    }
    Net.getRestClient().updateVideoPosition({
      'resourceId': localDetailResp!.resourceId,
      'resourceType': localDetailResp!.resourceType,
      'position': position,
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      DataCenterHelper.resumeSession();
    } else {
      DataCenterHelper.pauseSession();
    }
  }

  void addDataEpisode() async {
    logger("addDataEpisode start");
    if (localDetailResp == null) {
      logger("addDataEpisode localDetailResp null return");
      return;
    }
    DataCenterHelper.uploadLearningData(
      resourceId: localDetailResp!.resourceId!,
      resourceType: localDetailResp?.resourceType ?? 2,
      currentLsTimes: localDetailResp!.currentLsTimes ?? 0,
    );
  }

  void showLsTimeDialog() {
    showLsTimeModeDialog(localDetailResp!.resourceId, 2);
  }

  void changeControlMenuSort(int newIndex, int oldIndex) {
    if (newIndex > oldIndex) newIndex -= 1;
    final item = playerMenuItems.removeAt(oldIndex);
    playerMenuItems.insert(newIndex, item);
    Config().playerConfig.menuSort = playerMenuItems.map((item) => item.id).toList();
    Net.getRestClient().updatePlayerConfig({'menuSort': Config().playerConfig.menuSort});
  }

  void onSliderChange(double ratio) async {
    logger("onSliderChange called with ratio: $ratio");
    canChangeSeekBarRatio = false;

    // 在LS模式下，停止录音和播放
    if (videoKit.openLsMode.value) {
      if (recordingInLsMode.value) {
        logger("onSliderChange stopping recording in LS mode");
        _recognitionService.stop();
        recordingInLsMode.value = false;
      }
      recordSoundPlayer.stopPlayer();
    }

    // 执行seek操作
    var targetPosition = Duration(milliseconds: (ratio * totalDuration.value).toInt());
    logger("onSliderChange seeking to position: ${targetPosition.inMilliseconds}ms");
    await videoKit.seek(targetPosition);

    // 处理seek结束后的状态重置
    await videoKit.onVideoSeekChangeEnd();

    canChangeSeekBarRatio = true;
    logger("onSliderChange completed");
  }

  // 返回 false 表示不继续传递事件
  bool _interceptorPop(bool stopDefaultButtonEvent, RouteInfo info) {
    if (Get.context?.isLandscape == true) {
      videoKit.toggleFullscreen();
      return true;
    }
    return false;
  }

  void _uploadVideoInBackground(String videoPath) async {
    logger("开始后台上传视频文件: $videoPath");
    final url = await OssUtil().uploadUserPrivateVideoFile(videoPath);

    if (url != null) {
      logger("视频上传成功: $url");
      "视频上传成功！".toast;
      Net.getRestClient().updateUserLocalResource({
        'resourceId': localDetailResp!.resourceId,
        'resourceType': localDetailResp!.resourceType,
        'videoUrl': url,
      });
    } else {
      "视频上传失败，请重试".toast;
    }
  }

  @override
  void onClose() async {
    BackButtonInterceptor.remove(_interceptorPop);
    videoKit.exitNativeFullscreen();
    updateVideoPosition(videoKit.currentPositionInMilliseconds);
    addDataEpisode();
    SPUtil().saveSubtitleCover(_videoUrlOrPath, Config().subtitleCoverModel);
    ObsUtil().watchHistoryPositionChange.value = [localDetailResp?.resourceId ?? "", videoKit.currentPositionInMilliseconds];
    WidgetsBinding.instance.removeObserver(this);
    await recordSoundPlayer.closePlayer();
    _recognitionService.dispose();
    videoKit.destory();

    // 新增：批量上传本地录音到OSS并同步到后端（异步执行，不阻塞页面关闭）
    batchUploadSpeechEvaluationsToOssAndSync(speechEvaluationMap);

    super.onClose();
  }
}
