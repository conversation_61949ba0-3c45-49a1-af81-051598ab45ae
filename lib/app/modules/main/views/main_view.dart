import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/modules/datacenter/views/datacenter_view.dart';
import 'package:lsenglish/app/modules/mine/views/mine_view.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import '../../home/<USER>/home_view.dart';
import '../../notelist/views/notelist_view.dart';
import '../controllers/main_controller.dart';

class MainView extends GetView<MainController> {
  const MainView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [const HomeView(), const NotelistView(), const DatacenterView(), const MineView()];
    // final List<Widget> pages = [const ResourcelibView(), const HomeView(), const DatacenterView(), const MineView()];
    List<PersistentBottomNavBarItem> navBarsItems() {
      final iconSize = 36.whs;
      return [
        PersistentBottomNavBarItem(
          icon: ImageLoader(R.tab_home, size: iconSize, color: Get.theme.primaryColor),
          inactiveIcon: ImageLoader(R.tab_home, size: iconSize, color: Get.theme.primaryColor.withValues(alpha: 0.3)),
        ),
        PersistentBottomNavBarItem(
          icon: ImageLoader(R.tab_collect, size: iconSize, color: Get.theme.primaryColor),
          inactiveIcon: ImageLoader(R.tab_collect, size: iconSize, color: Get.theme.primaryColor.withValues(alpha: 0.3)),
        ),
        PersistentBottomNavBarItem(
          icon: ImageLoader(R.tab_data, size: iconSize, color: Get.theme.primaryColor),
          inactiveIcon: ImageLoader(R.tab_data, size: iconSize, color: Get.theme.primaryColor.withValues(alpha: 0.3)),
        ),
        PersistentBottomNavBarItem(
          icon: ImageLoader(R.tab_mine, size: iconSize, color: Get.theme.primaryColor),
          inactiveIcon: ImageLoader(R.tab_mine, size: iconSize, color: Get.theme.primaryColor.withValues(alpha: 0.3)),
        ),
      ];
    }

    return PersistentTabView(
      context,
      controller: controller.persistentTabController,
      screens: pages,
      items: navBarsItems(),
      backgroundColor:
          Theme.of(context).navigationBarTheme.backgroundColor ?? (Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white),
      handleAndroidBackButtonPress: true,
      resizeToAvoidBottomInset: true,
      stateManagement: true,
      navBarStyle: NavBarStyle.simple,
    );
  }
}
