name: lsenglish
description: "A new Flutter project."
publish_to: "none"

version: 1.0.1+1

environment:
  sdk: ">=3.2.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.6
  dio: ^5.3.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.4.0
  dio_cache_interceptor: ^4.0.3
  intl: ^0.20.2
  fluttertoast: ^8.2.12
  retrofit: ^4.6.0
  animations: ^2.0.11
  image_picker: ^1.1.2
  extended_image: ^10.0.1
  path: ^1.9.1
  permission_handler: ^12.0.1
  file_picker: ^10.2.0
  path_provider: ^2.1.5
  media_kit: ^1.2.0
  media_kit_video: ^1.3.0
  media_kit_libs_video: ^1.0.6
  flutter_sound: ^9.28.0
  volume_controller: ^3.4.0
  screen_brightness: ^2.1.5
  path_provider_foundation: ^2.4.1
  webview_flutter: ^4.13.0
  webview_flutter_android: ^4.7.0
  webview_flutter_wkwebview: ^3.22.0
  archive: ^4.0.7
  open_filex: ^4.7.0
  tuple: ^2.0.2
  focus_detector: ^2.0.1
  gap: ^3.0.1
  audio_service: ^0.18.18
  receive_sharing_intent: ^1.8.1
  sign_in_with_apple: ^7.0.1
  flutter_svg: ^2.2.0
  package_info_plus: ^8.3.0
  audio_session: ^0.2.2
  logger: ^2.6.0
  diff_match_patch: ^0.4.1
  crypto: ^3.0.6
  mime: ^2.0.0
  cupertino_icons: ^1.0.8
  permission_handler_platform_interface: ^4.3.0
  connectivity_plus: ^6.1.4
  scroll_to_index: ^3.0.1
  fl_chart: ^1.0.0
  convert: ^3.1.2
  persistent_bottom_nav_bar: ^6.2.1
  super_tooltip: ^2.1.0
  syncfusion_flutter_charts: ^30.1.40
  charset: ^2.0.1
  encrypt: ^5.0.3
  # firebase_core: ^3.6.0
  # firebase_analytics: ^11.3.3
  # firebase_crashlytics: ^4.3.1
  flutter_cache_manager: ^3.4.1
  flutter_chat_ui: ^2.7.0
  flutter_chat_core: ^2.7.0
  uuid: ^4.5.1
  back_button_interceptor: ^8.0.4
  apple_pay_plugin:
    path: ./apple_pay_plugin
  get_storage: ^2.1.1
  aliyunpan_sdk:
    path: ./aliyunpan_sdk
  aliyunpan_flutter_sdk_auth:
    path: ./aliyunpan_flutter_sdk_auth
  shared_preferences: ^2.5.3
  ffmpeg_kit_flutter_new: ^2.0.0
  flutter_smart_dialog: ^4.9.8+8
  flutter_plugin_stkouyu: ^1.0.29
  flutter_mxlogger: ^1.2.14
  smooth_sheets: ^0.14.0
  modal_bottom_sheet: ^3.0.0

dependency_overrides:
  package_info_plus: ^8.0.0
  visibility_detector: ^0.4.0+2
  js: ^0.7.1
  collection: 1.19.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  retrofit_generator: ">=7.0.0 <8.0.0"
  flutter_lints: ^2.0.0
  build_runner: ^2.4.8
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    # Add assets from the images directory to the application.
    - assets/images/
    - assets/json/
    - assets/
    - assets/sounds/ding.mp3
